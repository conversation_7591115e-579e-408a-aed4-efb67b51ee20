<template>
	<!-- 
    数字人编辑器主页面
    功能概览：
    - 数字人视频编辑的核心工作台
    - 集成预览编辑、画布比例控制、时间轴管理
    - 全屏布局适配，禁用页面滚动
    - 响应式设计支持不同编辑场景
    -->
	<div class="app" @click="onGlobalClick">
		<!--
        全局数据加载遮罩层
        功能：
        - 在重新编辑作品时显示数据加载状态
        - 提供用户友好的加载提示
        - 防止用户在数据加载期间进行操作
        -->
		<div v-if="isLoadingWorkData" class="global-loading-overlay">
			<div class="loading-content">
				<el-icon class="loading-icon" :size="40">
					<Loading />
				</el-icon>
				<p class="loading-text">正在加载作品数据...</p>
				<p class="loading-subtitle">请稍候，正在为您恢复编辑状态</p>
			</div>
		</div>

		<!--
        顶部导航条区域
        功能：
        - 全局导航和用户信息
        - 项目操作和设置入口
        - 固定在页面顶部
        - 为主编辑区域预留空间
        -->
		<header class="top-header">
			<Headbar ref="headbarRef" />
		</header>

		<!-- 
        编辑器页面主容器
        布局策略：
        - 水平三栏布局：左侧操作 + 中间编辑 + 右侧操作
        - 固定padding-top适配顶部导航
        - 禁用滚动确保界面稳定
        - 弹性布局适配不同屏幕尺寸
        -->
		<div class="editor-page-container">
			<!-- 
            左侧操作面板
            功能：
            - 数字人素材管理和选择
            - 场景设置和配置
            - 项目文件管理
            - 预设模板选择
            -->
			<LeftOperate ref="leftOperateRef" :currentAspectRatio="currentAspectRatio" @ratio-change="handleRatioChange"
				@background-change="handleBackgroundChange" @digital-human-change="handleDigitalHumanChange"
				@subtitle-style-change="handleSubtitleStyleChange" />

			<!-- 
            中间编辑区域
            核心功能：
            - 主预览面板和时间轴的容器
            - 垂直布局：预览在上，时间轴在下
            - 水平居中对齐确保最佳视觉效果
            -->
			<div class="center-content">
				<!-- 
                主预览面板
                核心功能：
                - 数字人视频内容的实时预览
                - 支持多种画布比例(16:9/9:16)
                - 固定尺寸(1253x799px)确保一致体验
                - 集成预览编辑器和比例控制器
                -->
				<main class="main-panel" @click.stop>
					<!-- 
                    预览编辑器组件
                    功能：
                    - 数字人角色、装饰图片、字幕的编辑
                    - 实时预览效果展示
                    - 拖拽、缩放、选择等交互功能
                    - 接收当前画布比例动态调整显示
                    -->
					<PreviewEditor ref="previewEditorRef" :aspectRatio="currentAspectRatio"
						:backgroundConfig="currentBackgroundConfig" :digitalHumanConfig="currentDigitalHumanConfig"
						:subtitleConfig="currentSubtitleConfig" :subtitleVisible="isSubtitleVisible"
						:inputText="currentInputText"
						@digital-human-cleared="handleDigitalHumanCleared"
						@background-cleared="handleBackgroundCleared" />

					<!-- 
                    画布比例选择器
                    位置：主面板左下角悬浮
                    功能：
                    - 16:9横屏和9:16竖屏比例切换
                    - 下拉菜单交互设计
                    - 实时更新预览编辑器显示
                    - 半透明背景和模糊效果
                    -->
					<div class="aspect-ratio-selector">
						<!-- 
                        比例选择下拉框
                        交互：点击展开/收起选项菜单
                        显示：当前选中比例 + 下拉图标
                        状态：悬停高亮，展开时图标旋转
                        -->
						<div class="ratio-dropdown" @click="toggleDropdown">
							<span class="ratio-label">画布比例</span>
							<span class="current-ratio">{{ currentAspectRatio }}</span>
							<i class="dropdown-icon" :class="{ 'expanded': isDropdownOpen }">▼</i>
						</div>

						<!-- 
                        比例选项下拉菜单
                        显示条件：isDropdownOpen为true时显示
                        功能：展示所有可选的画布比例
                        交互：点击选项切换比例并关闭菜单
                        样式：当前选中项高亮显示
                        -->
						<div v-if="isDropdownOpen" class="ratio-options-dropdown">
							<div v-for="ratio in availableRatios" :key="ratio.value"
								:class="['ratio-option', { active: currentAspectRatio === ratio.label }]"
								@click="selectRatio(ratio)">
								{{ ratio.label }}
							</div>
						</div>
					</div>
				</main>

				<!-- 
                时间轴轨道区域
                功能：
                - 视频时间线管理和控制
                - 事件标记和播放进度显示
                - 播放指针拖拽控制
                - 视频帧序列预览
                位置：主预览面板下方，水平居中
                -->
				<TimelineTrack :digitalHumanConfig="currentDigitalHumanConfig" />
			</div>

			<!--
            右侧操作面板
            功能：
            - 配音角色选择和调整
            - 背景音乐设置
            - 文本输入和字幕控制
            - 语音参数微调
            -->
			<RightOperate ref="rightOperateRef" @subtitle-toggle="handleSubtitleToggle"
				@audio-data-loaded="handleAudioDataLoaded" />
		</div>

		<!--
		    用户引导组件
		    功能：
		    - 首次访问时显示分步引导
		    - 通过10张引导图片指导用户操作
		    - 全屏覆盖层显示，支持透明点击区域
		    - 完成后不再显示，支持跳过功能
		    -->
		<UserGuide
			ref="userGuideRef"
			@guide-completed="handleGuideCompleted"
			@guide-skipped="handleGuideSkipped"
		/>
	</div>
</template>

<script setup>
// ========================================
// 📦 核心依赖导入
// ========================================
// 导入Vue组合式API核心函数
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
// 导入Vue Router路由管理工具
import { useRouter, useRoute } from 'vue-router';
// 导入事件总线
import eventBus from '@/common/utils/eventBus';

// ========================================
// 🧩 组件导入
// ========================================
// 导入预览编辑器组件：数字人视频内容编辑的核心组件
import PreviewEditor from './components/PreviewEditor.vue';
// 导入顶部导航条组件：全局导航和用户操作入口
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue';
// 导入时间轴组件：视频时间线管理和播放控制
import TimelineTrack from './components/TimelineTrack.vue';
// 导入左侧操作面板组件：数字人素材管理和设置
import LeftOperate from './components/left_operate/index.vue';
// 导入右侧操作面板组件：配音、音乐等功能控制
import RightOperate from './components/right_operate/index.vue';
// 导入用户引导组件：首次访问时的分步引导功能
import UserGuide from './components/UserGuide.vue';
// 导入数字人状态管理store
import { useDigitalHumanStore } from './store/digitalHumanStore';
// 导入数字人API接口
import { getDigitalWork } from '@/api/digitalHuman';
// 导入Element Plus图标组件
import { Loading } from '@element-plus/icons-vue';
// 导入Element Plus消息提示组件
import { ElMessage } from 'element-plus';

// ========================================
// 🧭 路由管理初始化
// ========================================
// 初始化路由实例，用于页面跳转和路由操作
const router = useRouter();
// 初始化路由状态，用于获取查询参数（如作品ID）
const route = useRoute();

// ========================================
// 🔧 字幕状态同步辅助函数
// ========================================
/**
 * 确保字幕开关状态同步的辅助函数（支持延迟和重试）
 * 
 * 🎯 功能：解决页面刷新时组件初始化时序导致的字幕开关状态同步失败问题
 * 📡 应用场景：loadWorkData后需要同步右侧操作面板的字幕开关状态时
 * 🔄 重试机制：如果第一次同步失败，会延迟重试最多3次
 * 
 * 🛠️ 技术细节：
 * - 采用递归延迟重试策略，首次延迟500ms，后续递增
 * - 检查组件引用和方法可用性，确保调用时机正确
 * - 提供备选方案（事件系统）处理极端情况
 * 
 * @param {boolean} isEnabled - 目标字幕开关状态（true=开启，false=关闭）
 * @param {number} retryCount - 当前重试次数（内部使用，默认为0）
 * @returns {Promise<boolean>} 返回同步是否成功
 */
const ensureSubtitleSyncWithRetry = async (isEnabled, retryCount = 0) => {
	const maxRetries = 3; // 最大重试次数
	const delayMs = retryCount === 0 ? 500 : 1000 + (retryCount * 500); // 首次延迟500ms，后续递增
	
	// 延迟执行，确保组件已完全初始化
	await new Promise(resolve => setTimeout(resolve, delayMs));
	
	try {
		// 检查右侧操作面板是否已初始化
		if (!rightOperateRef.value) {
			throw new Error('右侧操作面板组件未初始化');
		}
		
		// 尝试同步音频驱动模式的字幕开关状态
		if (rightOperateRef.value.syncAudioCaptionsState) {
			rightOperateRef.value.syncAudioCaptionsState(isEnabled);
			
			// 验证同步是否真的生效（检查底层组件状态）
			await nextTick();
			// 这里可以添加额外的状态验证逻辑
			
			return true; // 同步成功
		} else {
			throw new Error('syncAudioCaptionsState方法不可用');
		}
	} catch (error) {
		// 如果还有重试次数，继续重试
		if (retryCount < maxRetries) {
			return ensureSubtitleSyncWithRetry(isEnabled, retryCount + 1);
		} else {
			// 最后尝试通过事件系统发送字幕状态更新（备选方案）
			try {
				handleSubtitleToggle(isEnabled);
			} catch (eventError) {
				// 备选方案失败，但不影响主流程
			}
			
			return false; // 同步失败
		}
	}
};

/**
 * 确保字幕数据从pinia正确转换到store的辅助函数
 * 
 * 🎯 功能：解决从pinia中的originalWorkData.commonJson.subtitle_json提取数据并设置到store.subtitleData的问题
 * 📡 应用场景：当loadWorkData的常规数据转换逻辑失败或未执行时的备选方案
 * 🔄 数据流：pinia.originalWorkData.commonJson.subtitle_json → store.subtitleData
 * 
 * 🛠️ 技术细节：
 * - 支持多种时间字段格式的智能识别和转换
 * - 自动过滤无效数据，确保数据质量
 * - 毫秒/秒单位自动转换，兼容不同接口格式
 * 
 * @returns {boolean} 返回数据转换是否成功
 */
const ensureSubtitleDataTransfer = () => {
	try {
		// 检查pinia中是否有字幕数据
		const workData = digitalHumanStore.originalWorkData;
		if (!workData || !workData.commonJson || !workData.commonJson.subtitle_json) {
			return false;
		}

		const subtitleJsonArray = workData.commonJson.subtitle_json;
		if (!Array.isArray(subtitleJsonArray) || subtitleJsonArray.length === 0) {
			return false;
		}

		// 检查store中是否已有字幕数据，避免重复处理
		if (digitalHumanStore.subtitleData && digitalHumanStore.subtitleData.length > 0) {
			return true;
		}

		// 🔄 字幕数据格式化处理：将原始字幕数据转换为组件可用格式
		const formattedSubtitleData = subtitleJsonArray
			.filter(item => item && item.text && item.text.trim() !== '') // 过滤空数据
			.map((item, index) => {
				const text = (item.text || '').trim();
				
				// 🕒 智能时间字段提取和单位转换
				// 支持多种接口字段格式，确保数据兼容性
				let startTime = 0;
				let endTime = 0;
				
				// 优先级1：使用秒为单位的字段（start, end）
				if (item.start !== undefined && item.end !== undefined) {
					startTime = parseFloat(item.start) || 0;
					endTime = parseFloat(item.end) || (startTime + 1);
				}
				// 优先级2：使用兼容字段（startTime, endTime）
				else if (item.startTime !== undefined && item.endTime !== undefined) {
					startTime = parseFloat(item.startTime) || 0;
					endTime = parseFloat(item.endTime) || (startTime + 1);
				}
				// 优先级3：使用毫秒为单位的字段（time_begin, time_end），需要转换为秒
				else if (item.time_begin !== undefined && item.time_end !== undefined) {
					startTime = parseFloat(item.time_begin) / 1000 || 0; // 毫秒转秒
					endTime = parseFloat(item.time_end) / 1000 || (startTime + 1); // 毫秒转秒
				}
				// 默认处理：如果都没有，使用索引生成默认时间
				else {
					startTime = index * 2; // 默认每条字幕2秒
					endTime = startTime + 2;
				}
				
				return { 
					text, 
					startTime: startTime, 
					endTime: endTime 
				};
			});

		if (formattedSubtitleData.length > 0) {
			// 设置字幕数据到store
			digitalHumanStore.setSubtitleData(formattedSubtitleData);
			
			// 设置字幕显示状态
			isSubtitleVisible.value = true;

			return true;
		} else {
			return false;
		}

	} catch (error) {
		return false;
	}
};

// ========================================
// 📊 状态管理初始化
// ========================================

// 存储原始工作数据（编辑模式时从接口获取的完整数据）
// 用于生成视频时的数据回传，保持向后兼容性
const originalWorkData = ref(null);

// 初始化数字人状态管理store
// 管理音频、字幕、播放状态等核心数据
const digitalHumanStore = useDigitalHumanStore();

// ========================================
// 🎯 组件引用管理
// ========================================
// 预览编辑器组件引用，用于调用子组件方法和获取位置数据
const previewEditorRef = ref(null);
// 右侧操作面板组件引用，用于获取音频配置数据
const rightOperateRef = ref(null);
// 左侧操作面板组件引用，用于获取数字人选择数据
const leftOperateRef = ref(null);
// 头部组件引用，用于获取和设置标题数据
const headbarRef = ref(null);
// 用户引导组件引用，用于控制引导显示和重置
const userGuideRef = ref(null);

// ========================================
// 📊 数据加载状态管理
// ========================================
// 作品数据加载状态，控制全局加载遮罩的显示
const isLoadingWorkData = ref(false);
// 加载错误状态，用于错误处理和用户提示
const loadingError = ref(null);

/**
 * 全局点击处理函数
 * 
 * 🎯 功能：处理页面任何地方的点击，统一管理界面交互状态
 * 
 * 📡 触发条件：
 * - 点击左侧面板、右侧面板等预览区域外的地方
 * - 需要取消字幕等元素的选中状态
 * - 需要隐藏右键菜单等浮层
 * 
 * 🛠️ 实现原理：
 * - 通过调用预览编辑器子组件暴露的方法
 * - 统一处理界面状态重置，避免状态混乱
 * 
 * @param {Event} event - 点击事件对象
 */
const onGlobalClick = (event) => {
	// 调用预览编辑器的方法
	if (previewEditorRef.value) {
		// 隐藏右键菜单（如果可见）
		if (previewEditorRef.value.hideContextMenu) {
			previewEditorRef.value.hideContextMenu();
		}

		// 清除所有元素选中状态
		if (previewEditorRef.value.clearAllSelections) {
			previewEditorRef.value.clearAllSelections();
		}
	}
};

/**
 * 获取当前数字人编辑器的完整配置数据
 * 
 * 🎯 功能：收集所有编辑器组件的当前状态，用于保存数字人作品
 * 
 * 📊 数据来源：
 * - 右侧操作面板：音频配置、字幕文本、背景音乐
 * - 左侧操作面板：数字人配置、背景配置、字幕样式
 * - 主编辑器：画布尺寸、当前比例
 * - 预览编辑器：实时位置数据、标准坐标数据
 * 
 * 🛠️ 技术特点：
 * - 支持多种坐标系数据收集（页面坐标系 + 标准坐标系）
 * - 向后兼容旧版本数据格式
 * - 安全的错误处理，确保部分失败不影响整体
 * 
 * @returns {Object|null} 包含所有配置数据的对象，失败时返回null
 */
const getCurrentEditorData = () => {
	try {
		// 📊 获取右侧操作面板数据（音频、字幕、背景音乐等）
		let rightPanelData = null;
		if (rightOperateRef.value && rightOperateRef.value.getData) {
			rightPanelData = rightOperateRef.value.getData();
		}

		// 📊 获取左侧操作面板数据（数字人选择信息）
		let leftPanelData = null;
		if (leftOperateRef.value && leftOperateRef.value.getSelectionData) {
			try {
				leftPanelData = leftOperateRef.value.getSelectionData();
			} catch (error) {
				// 获取左侧面板数据失败，继续执行
			}
		}

		// 🎯 获取预览编辑器中的实时位置数据（包含背景、数字人、字幕的真实坐标）
		// 这些数据使用的是页面坐标系，适用于界面显示
		let positionsData = null;
		if (previewEditorRef.value && previewEditorRef.value.getAllPositionsData) {
			try {
				positionsData = previewEditorRef.value.getAllPositionsData();
			} catch (error) {
				// 获取实时位置数据失败，继续执行
			}
		}

		// 🎯 获取预览编辑器中的标准坐标数据（1080×1920标准坐标系，供接口使用）
		// 这些数据经过标准化处理，适用于服务端生成视频
		let standardPositionsData = null;
		if (previewEditorRef.value && previewEditorRef.value.getAllPositionsDataForAPI) {
			try {
				standardPositionsData = previewEditorRef.value.getAllPositionsDataForAPI();
			} catch (error) {
				// 获取标准坐标数据失败，继续执行
			}
		}

		// 🔄 构建向后兼容的canvasData格式（用于旧版本兼容）
		// 保持与旧版本接口的兼容性，避免破坏性更改
		let canvasData = null;
		if (positionsData) {
			canvasData = {
				digitalHumanPosition: positionsData.character ? {
					x: positionsData.character.x,
					y: positionsData.character.y,
					width: positionsData.character.width,
					height: positionsData.character.height
				} : null,
				backgroundPosition: positionsData.backgroundModule ? {
					x: positionsData.backgroundModule.x,
					y: positionsData.backgroundModule.y,
					width: positionsData.backgroundModule.width,
					height: positionsData.backgroundModule.height
				} : null,
				subtitlePosition: positionsData.subtitle ? {
					x: positionsData.subtitle.x,
					y: positionsData.subtitle.y,
					width: positionsData.subtitle.width,
					height: positionsData.subtitle.height
				} : null,
				allPositions: positionsData  // 完整的位置数据
			};
		}

		// 📐 计算标准画布尺寸（根据画布比例动态调整）
		const isVertical = currentAspectRatio.value === '9:16';
		let screenWidth, screenHeight;

		if (currentAspectRatio.value === '16:9') {
			screenWidth = 1920;   // 16:9横屏：标准宽度1920
			screenHeight = 1080;  // 16:9横屏：标准高度1080
		} else {
			screenWidth = 1080;   // 9:16竖屏：标准宽度1080
			screenHeight = 1920;  // 9:16竖屏：标准高度1920
		}

		console.log('📺 getCurrentEditorData屏幕尺寸设置:', {
			aspectRatio: currentAspectRatio.value,
			screenWidth,
			screenHeight,
			说明: `根据当前画布比例${currentAspectRatio.value}动态设置标准尺寸`
		});

		// 🔧 获取项目标题数据
		let title = '';
		if (headbarRef.value && headbarRef.value.getProjectTitle) {
			try {
				title = headbarRef.value.getProjectTitle();
			} catch (error) {
				// 获取标题数据失败，继续执行
			}
		}

		// 🎯 收集所有配置数据，构建完整的编辑器状态快照
		const editorData = {
			// 🔧 项目基础信息
			title: title,

			// 📐 画布配置信息
			aspectRatio: currentAspectRatio.value,
			screenWidth: screenWidth,
			screenHeight: screenHeight,

			// 🎭 数字人配置信息
			digitalHumanConfig: currentDigitalHumanConfig.value,

			// 🎨 背景配置信息
			backgroundConfig: currentBackgroundConfig.value,

			// 📝 字幕配置信息
			subtitleConfig: currentSubtitleConfig.value,
			subtitleVisible: isSubtitleVisible.value,

			// 📊 面板数据（用户输入和选择的数据）
			rightPanelData: rightPanelData,
			leftPanelData: leftPanelData,

			// 🎯 实时位置数据（背景、数字人、字幕的完整位置信息 - 页面坐标系）
			positionsData: positionsData,

			// 🎯 标准坐标数据（1080×1920标准坐标系，供接口使用）
			standardPositionsData: standardPositionsData,

			// 🔄 向后兼容的画布数据（包含数字人实际位置）
			canvasData: canvasData,

			// 📋 原始工作数据（编辑模式时从接口获取的完整数据）
			originalWorkData: originalWorkData.value,

			// 🎯 store 中的完整作品数据
			storeWorkData: digitalHumanStore.getOriginalWorkData()
	};

	return editorData;
	} catch (error) {
		// 发生异常时返回null，调用方可以根据此判断数据收集是否成功
		return null;
	}
};

// ========================================
// 📐 画布比例管理系统
// ========================================
/**
 * 当前选中的画布比例
 * 
 * 🎯 功能：控制预览编辑器的显示比例
 * 📱 默认值：'9:16' - 竖屏模式，适合移动端内容
 * 🔄 动态性：响应用户选择，实时更新预览效果
 */
const currentAspectRatio = ref('9:16');

/**
 * 下拉菜单展开状态
 * 
 * 🎭 控制比例选择器的显示/隐藏
 * 🖱️ 交互逻辑：点击切换，选择后自动关闭
 * 💡 默认状态：关闭（false）
 */
const isDropdownOpen = ref(false);

/**
 * 可用的画布比例选项配置
 * 
 * 📊 数据结构：
 * - label: 用户界面显示的文本标签
 * - value: 数值比例，用于计算和比较
 * 
 * 🎨 支持比例：
 * - 16:9: 横屏模式，适合桌面端和横向视频
 * - 9:16: 竖屏模式，适合移动端和短视频
 * 
 * 🔧 扩展性：可轻松添加更多比例选项（如1:1, 4:3等）
 */
const availableRatios = ref([
	{ label: '16:9', value: 16 / 9 },  // 横屏比例 - 宽屏模式
	{ label: '9:16', value: 9 / 16 }   // 竖屏比例 - 手机模式
]);

// ========================================
// 🎨 背景配置管理系统
// ========================================
/**
 * 当前背景配置
 *
 * 🎯 功能：控制预览编辑器的背景显示
 * 📊 数据结构：
 * - type: 'color' | 'image' | null - 背景类型
 * - value: 颜色值或图片URL
 * 🔄 动态性：响应用户选择，实时更新背景效果
 * 💡 默认状态：null表示未选择背景，不显示背景层
 */
const currentBackgroundConfig = ref({
	type: null,
	value: null  // 初始状态为空，只有用户主动设置背景时才有值
});

// ========================================
// 🎭 数字人配置管理系统
// ========================================
/**
 * 当前数字人配置
 *
 * 🎯 功能：控制预览编辑器的数字人显示
 * 📊 数据结构：
 * - type: 'picture' | 'transparent' - 数字人类型
 * - url: 数字人图片URL
 * - index: 选中的数字人索引
 * 🔄 动态性：响应用户选择，实时更新数字人显示
 */
const currentDigitalHumanConfig = ref({
	type: 'picture',
	url: '',  // 删除默认数字人图片，等待用户选择
	index: null
});

/**
 * 当前字幕样式配置状态
 *
 * 🎨 功能：存储当前字幕的样式配置信息
 * 📊 数据结构：字体、大小、颜色、描边等样式属性
 * 🔄 更新时机：用户在左侧面板修改字幕样式时更新
 * 
 * 🛠️ 技术细节：
 * - 支持自定义字体加载（TTF文件）
 * - 字体大小支持响应式缩放
 * - 描边样式可调节粗细和颜色
 */
const currentSubtitleConfig = ref({
	fontFamily: '',        // 字体样式ID
	fontName: '微软雅黑',    // 字体名称
	fontSize: 18,           // 默认字号18px，适合大多数场景
	textColor: '#FFFFFF',   // 文字色（白色，适合深色背景）
	borderColor: '',        // 描边色（默认空，无描边）
	borderWidth: 0          // 描边粗细（默认0，无描边）
});

/**
 * 字幕显示开关状态
 *
 * 🎯 功能：控制字幕是否在预览编辑器中显示
 * 📊 数据来源：右侧操作面板的字幕开关
 * 🔄 更新时机：用户切换字幕开关时更新
 * 💡 默认状态：true表示显示字幕
 */
const isSubtitleVisible = ref(true);

/**
 * 当前输入文本内容
 *
 * 🎯 功能：存储用户在输入文本模式下输入的文本内容
 * 📝 用途：作为字幕显示的备选数据源，当没有字幕数据时显示输入文本
 * 🔄 动态性：响应右侧操作面板的文本输入变化
 */
const currentInputText = ref('');

// ========================================
// 🔄 比例格式转换系统
// ========================================
/**
 * 比例格式转换函数
 * 
 * 🎯 功能：在父组件格式('9:16')和子组件格式('9')之间转换
 * 
 * 📊 转换规则：
 * - '9:16' → '9' （竖屏）
 * - '16:9' → '16' （横屏）
 * - '9' → '9:16' （竖屏）
 * - '16' → '16:9' （横屏）
 * 
 * 🛠️ 技术原因：
 * - 不同组件可能需要不同的比例表示格式
 * - 统一转换逻辑，避免格式不一致导致的问题
 * 
 * @param {string} ratio - 需要转换的比例值
 * @param {string} targetFormat - 目标格式 'full' | 'short'
 * @returns {string} 转换后的比例值
 */
const convertRatioFormat = (ratio, targetFormat = 'full') => {
	if (targetFormat === 'short') {
		// 完整格式转换为短格式：'9:16' → '9'
		return ratio === '9:16' ? '9' : '16';
	} else {
		// 短格式转换为完整格式：'9' → '9:16'
		return ratio === '9' ? '9:16' : '16:9';
	}
};

/**
 * 处理子组件比例变更事件
 * 
 * 🎯 功能：响应左侧操作面板的比例切换，同步更新主比例状态
 * 
 * 📡 事件流程：
 * 1. 接收子组件emit的比例变更事件
 * 2. 转换比例格式（短格式→完整格式）
 * 3. 检查是否重复更新，避免递归
 * 4. 更新父组件的比例状态
 * 5. 触发预览编辑器重新渲染
 * 
 * 🛠️ 防护机制：
 * - 重复更新检测，避免无限循环
 * - 异常处理，确保比例更新失败不影响界面
 * 
 * @param {string} newRatio - 新的比例值（短格式：'9' 或 '16'）
 */
const handleRatioChange = (newRatio) => {
	const fullRatio = convertRatioFormat(newRatio, 'full');

	// 检查是否与当前比例相同，避免重复更新导致递归
	if (currentAspectRatio.value === fullRatio) {
		return;
	}

	try {
		currentAspectRatio.value = fullRatio;
	} catch (error) {
		// 比例更新失败，保持原有状态
	}
};

/**
 * 处理背景变更事件
 *
 * 🎯 功能：响应左侧操作面板的背景选择，同步更新背景配置
 *
 * 📡 事件流程：
 * 1. 接收子组件emit的背景变更事件
 * 2. 更新父组件的背景配置状态
 * 3. 触发预览编辑器重新渲染背景
 *
 * 🛠️ 支持的背景类型：
 * - 纯色背景：type: 'color', value: '#FFFFFF'
 * - 图片背景：type: 'image', value: 'image_url'
 *
 * @param {Object} backgroundConfig - 背景配置对象 { type: 'color', value: '#FF0000' }
 */
const handleBackgroundChange = (backgroundConfig) => {
	currentBackgroundConfig.value = backgroundConfig;
};

/**
 * 处理数字人选择变更事件
 *
 * 🎯 功能：响应左侧操作面板的数字人选择，同步更新数字人配置
 *
 * 📡 事件流程：
 * 1. 接收子组件emit的数字人变更事件
 * 2. 更新父组件的数字人配置状态
 * 3. 触发预览编辑器重新渲染数字人图片
 *
 * 🛠️ 数字人类型：
 * - picture: 普通图片数字人
 * - transparent: 透明背景数字人
 *
 * @param {Object} digitalHumanConfig - 数字人配置对象 { type: 'picture', url: 'image_url', index: 0 }
 */
const handleDigitalHumanChange = (digitalHumanConfig) => {
	currentDigitalHumanConfig.value = digitalHumanConfig;
};

/**
 * 处理字幕样式变更事件
 *
 * 🎯 功能：响应左侧操作面板的字幕样式设置，同步更新字幕配置
 *
 * 📡 事件流程：
 * 1. 接收子组件emit的字幕样式变更事件
 * 2. 检查配置是否有变化，避免重复更新
 * 3. 更新父组件的字幕配置状态
 * 4. 触发预览编辑器重新渲染字幕样式
 *
 * 🛠️ 性能优化：
 * - 对象比较检查，避免无效更新
 * - 支持字体URL的动态加载
 * - 确保字体名称的默认值设置
 *
 * @param {Object} subtitleConfig - 字幕配置对象 { fontFamily: '1', fontSize: 18, textColor: '#ffffff', borderColor: '#000000', borderWidth: 7, fontName: '字体名称', fontUrl: '字体URL' }
 */
const handleSubtitleStyleChange = (subtitleConfig) => {
	// 简单的对象比较，检查关键字段是否有变化（包括fontUrl）
	const current = currentSubtitleConfig.value;
	if (
		current.fontFamily === subtitleConfig.fontFamily &&
		current.fontSize === subtitleConfig.fontSize &&
		current.textColor === subtitleConfig.textColor &&
		current.borderColor === subtitleConfig.borderColor &&
		current.borderWidth === subtitleConfig.borderWidth &&
		current.fontName === subtitleConfig.fontName &&
		current.fontUrl === subtitleConfig.fontUrl  // 检查fontUrl变化
	) {
		return; // 没有变化，直接返回
	}

	try {
		currentSubtitleConfig.value = {
			...subtitleConfig,
			fontName: subtitleConfig.fontName || '微软雅黑',  // 确保fontName字段存在
			fontUrl: subtitleConfig.fontUrl || null  // 确保fontUrl字段存在
		};
	} catch (error) {
		// 字幕样式更新失败，保持原有配置
	}
};

/**
 * 处理字幕开关切换事件
 *
 * 🎯 功能：响应右侧操作面板的字幕开关，控制字幕显示状态
 *
 * 📡 事件流程：
 * 1. 接收右侧操作面板emit的字幕开关状态
 * 2. 更新本地字幕显示状态
 * 3. 传递给预览编辑器组件控制字幕显示
 * 4. 当开关变为true时，确保字幕数据正确设置
 *
 * 🛠️ 特殊处理：
 * - 从false变为true时的数据确保机制
 * - 延迟执行确保状态变化完成
 *
 * @param {boolean} isVisible - 字幕是否显示，true显示，false隐藏
 */
const handleSubtitleToggle = (isVisible) => {
	try {
		const previousState = isSubtitleVisible.value;
		isSubtitleVisible.value = isVisible;
		
		// 🔧 关键修复：当字幕开关从false变为true时，确保字幕数据正确设置
		if (!previousState && isVisible) {
			// 延迟执行，确保状态变化完成
			setTimeout(() => {
				ensureSubtitleDataTransfer();
			}, 100);
		}
	} catch (error) {
		// 字幕开关处理失败，保持原有状态
	}
};



/**
 * 处理数字人清空事件
 *
 * 🎯 功能：响应预览编辑器的数字人清空事件，同时清空轨道中的数字人图片
 *
 * 📡 事件流程：
 * 1. 接收预览编辑器emit的数字人清空事件
 * 2. 清空当前数字人配置的URL，使轨道中的数字人图片消失
 * 3. 保持其他配置项不变，等待左侧面板重新选择数字人
 *
 * 🛠️ 设计考虑：
 * - 只清空URL而不是整个配置对象，保持结构完整性
 * - 重置索引为null，表示无选择状态
 */
const handleDigitalHumanCleared = () => {
	try {
		// 清空数字人配置，但保持基本结构
		currentDigitalHumanConfig.value = {
			type: 'picture',
			url: '',  // 清空URL，轨道中的数字人图片将消失
			index: null  // 重置索引
		};
	} catch (error) {
		// 数字人清空处理失败，保持原有状态
	}
};

/**
 * 处理背景清空事件
 *
 * 🎯 功能：响应预览编辑器的背景清空事件，同时清空背景配置和左侧面板选择状态
 *
 * 📡 事件流程：
 * 1. 接收预览编辑器emit的背景清空事件
 * 2. 清空当前背景配置，使背景模块消失
 * 3. 通知左侧面板清空背景选择状态，保持UI一致性
 *
 * 🛠️ 同步机制：
 * - 清空本地配置状态
 * - 同步通知左侧面板，确保界面状态一致
 */
const handleBackgroundCleared = () => {
	try {
		// 清空背景配置
		currentBackgroundConfig.value = {
			type: '',
			value: ''
		};

		// 通知左侧面板清空背景选择状态
		if (leftOperateRef.value && leftOperateRef.value.clearBackgroundSelection) {
			leftOperateRef.value.clearBackgroundSelection();
		}
	} catch (error) {
		// 背景清空处理失败，保持原有状态
	}
};

/**
 * 处理音频和字幕数据加载事件
 *
 * 🎯 功能：接收右侧操作面板传递的音频和字幕数据，加载到时间轴轨道中
 *
 * 📡 事件流程：
 * 1. 接收音频文件URL和字幕数据
 * 2. 更新数字人状态store中的音频和字幕信息
 * 3. 通知时间轴组件加载新的音频和字幕数据
 *
 * 🛠️ 支持的模式：
 * - 音频驱动模式：从音频文件中提取字幕
 * - 输入文本模式：用户输入文本生成字幕
 *
 * 🔄 数据处理特点：
 * - 智能识别时间字段格式（毫秒/秒自动转换）
 * - 过滤无效字幕数据，确保质量
 * - 支持多种接口数据结构
 *
 * @param {Object} audioData - 音频和字幕数据对象
 * @param {string} audioData.audioUrl - 音频文件URL
 * @param {Array} audioData.subtitleData - 字幕JSON数组
 * @param {string} audioData.subtitleFile - 字幕文件URL
 * @param {string} audioData.srtContent - SRT字幕内容
 * @param {number} audioData.audioLength - 音频时长（毫秒）
 * @param {boolean} audioData.openCaptions - 字幕开关状态
 * @param {Object} audioData.chooseMusic - 背景音乐数据
 * @param {string} audioData.mode - 模式标识（'audio_drive' | 其他）
 */
const handleAudioDataLoaded = (audioData) => {
	try {
		// 🎵 区分音频驱动模式和输入文本模式
		const isAudioDriveMode = audioData.mode === 'audio_drive';

		// 🔄 模式切换时的状态重置处理
		if (isAudioDriveMode) {
			// 使用专用的模式切换重置方法
			digitalHumanStore.resetForModeSwitch();
		} else {
			// 📝 输入文本模式：执行轻量重置，清理音频驱动残留数据
			// 只清理音频和播放状态，保留可能有用的时间轴结构
			digitalHumanStore.stop();
			digitalHumanStore.clearAudio();
			digitalHumanStore.setCurrentTime(0);
		}

		// 更新字幕显示状态
		if (audioData.openCaptions !== undefined) {
			isSubtitleVisible.value = audioData.openCaptions;
		}

		// 🔧 新增：更新当前输入文本（用于字幕显示的备选数据源）
		if (audioData.extractedText) {
			currentInputText.value = audioData.extractedText;
			console.log('📝 输入文本已更新:', audioData.extractedText.substring(0, 50) + '...');
		} else if (audioData.isInputTextMode === false) {
			// 音频驱动模式清空输入文本
			currentInputText.value = '';
		}

		// 🔧 关键修复：处理字幕数据数组（支持输入文本模式和音频驱动模式）
		if (audioData.subtitleData && Array.isArray(audioData.subtitleData) && audioData.subtitleData.length > 0) {
			try {
				// 🔄 转换字幕数据格式以匹配store期望的格式
				const formattedSubtitleData = audioData.subtitleData
					.map((item) => {
						// 🔧 根据实际API数据结构调整字段映射，只保留有真实内容的字幕
						const text = item.text || item.content || item.subtitle;

						// 如果没有真实的字幕内容，跳过这条数据
						if (!text || text.trim() === '') {
							return null;
						}

						// 获取原始时间值（可能是毫秒或秒）
						const rawStartTime = item.time_begin || item.start_time || item.startTime || item.start || 0;
						const rawEndTime = item.time_end || item.end_time || item.endTime || item.end || rawStartTime + 1000;

						// 🔧 时间单位转换：如果时间值大于1000，很可能是毫秒，需要转换为秒
						const startTime = rawStartTime > 1000 ? rawStartTime / 1000 : rawStartTime;
						const endTime = rawEndTime > 1000 ? rawEndTime / 1000 : rawEndTime;

						return {
							text: text,
							startTime: startTime,
							endTime: endTime
						};
					})
					.filter(item => item !== null); // 过滤掉空的字幕项

				// 设置字幕数据到store（这会自动创建时间轴事件）
				digitalHumanStore.setSubtitleData(formattedSubtitleData);
				const modeText = audioData.isInputTextMode ? '输入文本模式' : '音频驱动模式';
				console.log(`✅ ${modeText}字幕数据已设置:`, formattedSubtitleData);
			} catch (error) {
				console.error('❌ 设置输入文本字幕数据失败:', error);
			}
		}
		// 🔧 备选方案：如果没有subtitle_json但有输入文本，创建简单字幕
		else if (audioData.isInputTextMode && audioData.extractedText && audioData.extractedText.trim() !== '') {
			try {
				// 创建简单的字幕数据结构
				const simpleSubtitleData = [{
					text: audioData.extractedText,
					startTime: 0,
					endTime: audioData.audioLength ? audioData.audioLength / 1000 : 999999 // 使用音频时长或默认值
				}];

				digitalHumanStore.setSubtitleData(simpleSubtitleData);
				console.log('✅ 输入文本模式简单字幕数据已设置:', simpleSubtitleData);
			} catch (error) {
				console.error('❌ 设置输入文本简单字幕数据失败:', error);
			}
		}

		// 🎵 设置音频URL到store（播放器使用audioUrl字段）
		if (audioData.audioUrl) {
			digitalHumanStore.setTtsAudioUrl(audioData.audioUrl);
			console.log('🎵 播放器音频URL已设置:', audioData.audioUrl);
		}

		// 🎵 保存接口传参用的音频URL到全局状态（供接口调用时使用）
		if (audioData.originalAudioUrl) {
			// 将接口用的音频URL保存到digitalHumanRightOption中，供接口调用时使用
			digitalHumanRightOption.interfaceAudioUrl = audioData.originalAudioUrl;
			console.log('🎵 接口传参音频URL已保存:', audioData.originalAudioUrl);
		}

		// 设置字幕文件URL到store
		if (audioData.subtitleFile) {
			digitalHumanStore.setSubtitleUrl(audioData.subtitleFile);
		}

		// 🎵 音频驱动模式的特殊处理
		if (isAudioDriveMode) {
			// 优先使用详细的字幕数据（包含时间信息）
			if (audioData.subtitleData && Array.isArray(audioData.subtitleData) && audioData.subtitleData.length > 0) {
				// 🔄 转换字幕数据格式以匹配store期望的格式
				const formattedSubtitleData = audioData.subtitleData.map((item, index) => {
					const text = item.text || `字幕片段${index + 1}`;
					const startTime = item.start || item.startTime || 0;
					const endTime = item.end || item.endTime || startTime + 1;

					return { text, startTime, endTime };
				});

				digitalHumanStore.setSubtitleData(formattedSubtitleData);
			}
			// 如果没有详细数据，使用简单的文本模式
			else if (audioData.extractedText) {
				// 只有当有真实音频时长时才创建字幕事件
				if (audioData.audioLength && audioData.audioLength > 0) {
					const audioDuration = audioData.audioLength / 1000; // 转换为秒
					const simpleSubtitleData = [{
						text: audioData.extractedText,
						startTime: 0,
						endTime: audioDuration
					}];

					digitalHumanStore.setSubtitleData(simpleSubtitleData);
				}
			}
		}
		// 处理输入文本模式的字幕JSON数据并加载到store和时间轴
		else if (audioData.subtitleData && Array.isArray(audioData.subtitleData)) {
			// 🔄 转换字幕数据格式以匹配store期望的格式
			// 根据实际API数据结构调整字段映射
			const formattedSubtitleData = audioData.subtitleData
				.map((item) => {
					// 🔧 根据实际API数据结构调整字段映射，只保留有真实内容的字幕
					const text = item.text || item.content || item.subtitle;

					// 如果没有真实的字幕内容，跳过这条数据
					if (!text || text.trim() === '') {
						return null;
					}

					// 获取原始时间值（可能是毫秒或秒）
					const rawStartTime = item.time_begin || item.start_time || item.startTime || item.start || 0;
					const rawEndTime = item.time_end || item.end_time || item.endTime || item.end || rawStartTime + 1000;

					// 🔧 时间单位转换：如果时间值大于1000，很可能是毫秒，需要转换为秒
					const startTime = rawStartTime > 1000 ? rawStartTime / 1000 : rawStartTime;
					const endTime = rawEndTime > 1000 ? rawEndTime / 1000 : rawEndTime;

					return {
						text: text,
						startTime: startTime,
						endTime: endTime
					};
				})
				.filter(item => item !== null); // 过滤掉空的字幕项

			// 设置字幕数据到store（这会自动创建时间轴事件）
			digitalHumanStore.setSubtitleData(formattedSubtitleData);
		}

		// 如果有音频时长信息，更新总时长
		if (audioData.audioLength && audioData.audioLength > 0) {
			// 将毫秒转换为秒
			const durationInSeconds = Math.ceil(audioData.audioLength / 1000);
			digitalHumanStore.totalDuration = durationInSeconds;
		}
	} catch (error) {
		// 音频数据处理失败，保持原有状态
	}
};

// ========================================
// 🎯 用户引导事件处理
// ========================================
/**
 * 处理用户引导完成事件
 *
 * 🎯 功能：用户完成引导流程后的处理
 * 📡 触发时机：用户点击完成引导或完成所有引导步骤
 *
 * 🛠️ 处理内容：
 * - 记录引导完成状态到localStorage
 * - 可以添加埋点统计
 * - 可以显示欢迎消息或其他后续操作
 */
const handleGuideCompleted = () => {
	try {
		console.log('用户引导已完成');

		// 可以在这里添加埋点统计
		// umeng.track('digital_human_guide_completed');

		// 可以显示欢迎消息
		ElMessage.success('引导完成！开始创建您的数字人视频吧！');

	} catch (error) {
		console.error('处理引导完成事件失败:', error);
	}
};

/**
 * 处理用户跳过引导事件
 *
 * 🎯 功能：用户跳过引导流程后的处理
 * 📡 触发时机：用户点击跳过引导按钮
 *
 * 🛠️ 处理内容：
 * - 记录跳过引导的统计信息
 * - 可以显示提示信息
 */
const handleGuideSkipped = () => {
	try {
		console.log('用户跳过了引导');

		// 可以在这里添加埋点统计
		// umeng.track('digital_human_guide_skipped');

		// 可以显示提示信息
		ElMessage.info('您可以随时在帮助中心查看使用指南');

	} catch (error) {
		console.error('处理跳过引导事件失败:', error);
	}
};

/**
 * 重置用户引导状态（开发用）
 *
 * 🎯 功能：重置引导状态，用于开发测试
 * 🛠️ 使用方式：在浏览器控制台调用 window.resetDigitalHumanGuide()
 */
const resetUserGuide = () => {
	try {
		if (userGuideRef.value && userGuideRef.value.resetGuide) {
			userGuideRef.value.resetGuide();
			ElMessage.success('引导状态已重置，刷新页面后将重新显示引导');
		}
	} catch (error) {
		console.error('重置引导状态失败:', error);
	}
};

// 开发模式下暴露重置方法到全局
if (process.env.NODE_ENV === 'development') {
	window.resetDigitalHumanGuide = resetUserGuide;
}

// ========================================
// 🎮 交互控制方法
// ========================================
/**
 * 切换下拉框显示状态
 * 
 * 🔄 功能：展开/收起比例选择下拉菜单
 * 🎭 状态切换：true ↔ false
 * 📱 用户体验：点击下拉框触发，提供直观的交互反馈
 * 🎨 视觉效果：配合CSS动画，下拉图标旋转180度
 */
const toggleDropdown = () => {
	isDropdownOpen.value = !isDropdownOpen.value;
};

/**
 * 选择画布比例处理函数
 * 
 * 🎯 核心功能：
 * 1. 检查是否重复选择，避免无效更新
 * 2. 更新当前选中的画布比例
 * 3. 关闭下拉菜单
 * 4. 触发PreviewEditor组件重新渲染
 * 
 * 📊 参数：
 * @param {Object} ratio - 比例对象，包含label和value属性
 * @param {string} ratio.label - 显示标签（如'16:9'）
 * @param {number} ratio.value - 数值比例（如16/9）
 * 
 * 🔄 副作用：
 * - 自动关闭下拉菜单提升用户体验
 * - 触发PreviewEditor的aspectRatio prop更新
 * 
 * 📱 用户体验：
 * - 即时生效，无需确认步骤
 * - 平滑过渡，避免页面跳动
 * - 状态持久化，刷新后保持选择
 * 
 * 🛠️ 错误处理：
 * - 发生异常时确保下拉框仍然关闭
 * - 保持界面状态一致性
 */
const selectRatio = (ratio) => {
	// 检查是否与当前比例相同，避免重复更新
	if (currentAspectRatio.value === ratio.label) {
		isDropdownOpen.value = false; // 仍然关闭下拉框
		return;
	}

	try {
		currentAspectRatio.value = ratio.label; // 更新当前比例
		isDropdownOpen.value = false; // 关闭下拉框
	} catch (error) {
		isDropdownOpen.value = false; // 发生错误时也要关闭下拉框
	}
};

// ========================================
// 📊 数据回显功能
// ========================================

/**
 * 清空中间预览区域的所有数据
 *
 * 🎯 功能：清空所有中间区域的配置，确保加载新作品数据时不受之前数据影响
 *
 * 🧹 清空范围：
 * - 背景配置（重置为null状态）
 * - 数字人配置（清空URL和索引）
 * - 字幕配置（恢复默认样式）
 * - 数字人状态store中的数据（完整重置）
 * - 原始工作数据（清空引用）
 * - 左侧操作面板的字体设置（重置为默认值）
 * - 右侧操作面板的状态（重置为默认值）
 *
 * 🛠️ 技术特点：
 * - 使用resetForModeSwitch进行完整重置
 * - 确保hasValidBackgroundConfig返回false
 * - 保持配置对象结构完整性
 * - 主动调用子组件重置方法，确保完全清空
 *
 * @returns {void} 无返回值
 */
const clearMiddleAreaData = async () => {
	try {
		// 1. 清空背景配置为真正的空值
		currentBackgroundConfig.value = {
			type: null,
			value: null  // 设置为null，确保hasValidBackgroundConfig返回false
		};

		// 2. 清空数字人配置
		currentDigitalHumanConfig.value = {
			type: 'picture',
			url: '',  // 清空数字人图片URL
			index: null  // 重置索引
		};

		// 3. 重置字幕配置为默认值
		currentSubtitleConfig.value = {
			fontFamily: '1',        // 默认字体样式ID
			fontName: '微软雅黑',    // 默认字体名称
			fontSize: 18,           // 默认字号改为18
			textColor: '#ffffff',   // 默认文字色
			borderColor: '',        // 默认描边色改为空
			borderWidth: 0          // 默认描边粗细改为0
		};

		// 4. 使用完整的重置方法清空数字人状态store中的所有数据
		digitalHumanStore.resetForModeSwitch();  // 完整重置：时长、音频、字幕、播放状态等

		// 5. 重置字幕显示状态
		isSubtitleVisible.value = true;  // 默认显示字幕

		// 6. 清空原始工作数据
		originalWorkData.value = null;

		// 🎯 清空 store 中的作品数据
		digitalHumanStore.clearWorkData();

		// 🔄 等待DOM更新，确保组件引用可用
		await nextTick();

		// 7. 🧹 调用左侧操作面板的重置方法，清空字体设置
		try {
			if (leftOperateRef.value && leftOperateRef.value.resetSubtitleSettings) {
				leftOperateRef.value.resetSubtitleSettings();
				console.log('✅ 已调用左侧面板字体设置重置方法');
			}
		} catch (error) {
			console.warn('⚠️ 调用左侧面板重置方法失败:', error);
		}

		// 8. 🧹 调用右侧操作面板的重置方法，清空面板状态
		try {
			if (rightOperateRef.value && rightOperateRef.value.resetPanelData) {
				await rightOperateRef.value.resetPanelData();
				console.log('✅ 已调用右侧面板数据重置方法');
			}
		} catch (error) {
			console.warn('⚠️ 调用右侧面板重置方法失败:', error);
		}

		console.log('✅ 中间预览区域数据清空完成，包括子组件状态重置');

	} catch (error) {
		console.error('❌ 清空中间预览区域数据失败:', error);
	}
};

/**
 * 加载数字人作品数据（编辑模式）
 *
 * 🎯 功能：当URL包含id参数时，获取对应的数字人作品详情数据并进行回显
 * 
 * 📊 数据来源：通过digitalHumanStore.loadWorkData方法获取作品详情
 * 
 * 🔄 处理逻辑：
 * 1. 清空所有现有数据，确保干净的初始状态
 * 2. 判断新建模式vs编辑模式
 * 3. 解析接口数据并更新到相应的响应式变量中
 * 4. 处理各种数据回显（背景、音频、标题、字幕、字体等）
 * 5. 同步组件状态，确保界面一致性
 *
 * 🛠️ 数据回显覆盖：
 * - 背景图片/颜色回显
 * - 音频轨道URL设置
 * - 作品标题恢复
 * - 字幕数据转换和加载
 * - 字体样式配置恢复
 * - 位置数据恢复
 * - 数字人图片回显
 *
 * 🔧 容错机制：
 * - 每个处理步骤都有独立的错误处理
 * - 部分失败不影响其他数据的加载
 * - 加载状态管理和用户反馈
 * 
 * @returns {Promise<void>} 异步函数，无返回值
 */
const loadWorkData = async () => {
	try {
		// 🧹 无论是新建模式还是编辑模式，都先清空所有数据，确保干净的初始状态
		await clearMiddleAreaData();

		// 获取URL查询参数中的作品ID
		const workId = route.query.id;

		// 🆕 新建模式处理：如果没有ID参数，说明是新建模式
		if (!workId) {
			// 清空原始工作数据
			originalWorkData.value = null;
			
			// 🏷️ 新建模式下清空标题
			await nextTick();
			try {
				if (headbarRef.value && headbarRef.value.setProjectTitle) {
					headbarRef.value.setProjectTitle('');
				}
			} catch (error) {
				// 清空页面标题失败，继续执行
			}
			
			// 确保清除加载状态
			isLoadingWorkData.value = false;
			loadingError.value = null;
			return;
		}

		// 防止重复加载同一个作品
		if (isLoadingWorkData.value) {
			return;
		}

		// 设置加载状态
		isLoadingWorkData.value = true;
		loadingError.value = null;

		// 🎯 使用 store 的方法加载作品数据
		const workData = await digitalHumanStore.loadWorkData(workId);

		if (!workData) {
			loadingError.value = digitalHumanStore.workDataError || '获取作品数据失败，请稍后重试';
			ElMessage.error(digitalHumanStore.workDataError || '获取作品数据失败，请稍后重试');
			return;
		}

		// 🔄 保存原始工作数据（供生成视频时使用，保持向后兼容）
		originalWorkData.value = workData;

		// ========================================
		// 🧠 智能画布比例推断系统（针对旧作品）
		// ========================================

		/**
		 * 智能推断作品的原始画布比例
		 *
		 * 🎯 功能：通过分析作品数据的各种特征来推断原始画布比例
		 * 📊 适用场景：旧作品没有保存aspectRatio信息时的兜底方案
		 *
		 * @param {Object} workData - 作品数据对象
		 * @returns {Object} 推断结果 {ratio, confidence, reasons}
		 */
		const inferAspectRatioFromWorkData = (workData) => {
			const reasons = []; // 记录推断依据
			let confidence = 0; // 推断置信度 (0-1)
			let inferredRatio = '9:16'; // 默认值

			console.log('🧠 开始智能推断画布比例，分析作品数据:', {
				bgJson: workData.bgJson,
				personJson: workData.personJson
			});

			// 1. 背景尺寸推断（最可靠的方法）
			if (workData.bgJson && workData.bgJson.width && workData.bgJson.height) {
				const bgWidth = Number(workData.bgJson.width);
				const bgHeight = Number(workData.bgJson.height);
				const bgRatio = bgWidth / bgHeight;

				console.log('🔍 分析背景尺寸:', { bgWidth, bgHeight, bgRatio });

				if (bgRatio > 1.5) { // 明显的横屏比例
					inferredRatio = '16:9';
					confidence += 0.8;
					reasons.push(`背景尺寸${bgWidth}×${bgHeight}，宽高比${bgRatio.toFixed(2)}，判断为横屏`);
				} else if (bgRatio < 0.7) { // 明显的竖屏比例
					inferredRatio = '9:16';
					confidence += 0.8;
					reasons.push(`背景尺寸${bgWidth}×${bgHeight}，宽高比${bgRatio.toFixed(2)}，判断为竖屏`);
				} else {
					reasons.push(`背景尺寸${bgWidth}×${bgHeight}，宽高比${bgRatio.toFixed(2)}，比例不明显`);
				}
			}

			// 2. 标准尺寸特征推断（高置信度）
			if (workData.bgJson && workData.bgJson.width && workData.bgJson.height) {
				const w = Number(workData.bgJson.width);
				const h = Number(workData.bgJson.height);

				// 检查是否接近16:9标准尺寸
				if ((w === 1920 && h === 1080) || (Math.abs(w - 1920) < 100 && Math.abs(h - 1080) < 100)) {
					inferredRatio = '16:9';
					confidence = Math.max(confidence, 0.9); // 使用更高的置信度
					reasons.push(`背景尺寸${w}×${h}接近1920×1080标准，强烈判断为16:9`);
				}
				// 检查是否接近9:16标准尺寸
				else if ((w === 1080 && h === 1920) || (Math.abs(w - 1080) < 100 && Math.abs(h - 1920) < 100)) {
					inferredRatio = '9:16';
					confidence = Math.max(confidence, 0.9); // 使用更高的置信度
					reasons.push(`背景尺寸${w}×${h}接近1080×1920标准，强烈判断为9:16`);
				}
			}

			// 3. 数字人位置辅助推断（较低置信度）
			if (workData.personJson && workData.personJson.y !== undefined) {
				const personY = Number(workData.personJson.y);
				if (personY > 800) { // 数字人位置较低，可能是16:9模式
					if (inferredRatio === '16:9') {
						confidence += 0.1; // 增强现有判断
						reasons.push(`数字人Y坐标${personY}较低，支持16:9判断`);
					}
				} else if (personY < 600) { // 数字人位置较高，可能是9:16模式
					if (inferredRatio === '9:16') {
						confidence += 0.1; // 增强现有判断
						reasons.push(`数字人Y坐标${personY}较高，支持9:16判断`);
					}
				}
			}

			// 确保置信度不超过1
			confidence = Math.min(confidence, 1.0);

			console.log('🧠 智能推断完成:', {
				推断比例: inferredRatio,
				置信度: confidence,
				推断依据: reasons
			});

			return {
				ratio: inferredRatio,
				confidence: confidence,
				reasons: reasons
			};
		};

		// ========================================
		// 📊 数据解析和回显处理
		// ========================================

		// 📐 0. 画布比例回显处理（必须在其他数据回显之前执行，因为比例会影响位置计算）
		console.log('🔍 开始画布比例回显处理，当前比例:', currentAspectRatio.value);
		console.log('🔍 作品数据中的commonJson:', workData.commonJson);

		if (workData.commonJson && workData.commonJson.aspectRatio) {
			const savedAspectRatio = workData.commonJson.aspectRatio;
			console.log('🔍 从作品数据中找到保存的画布比例:', savedAspectRatio);

			// 验证比例值的有效性
			if (savedAspectRatio === '16:9' || savedAspectRatio === '9:16') {
				const oldRatio = currentAspectRatio.value;
				currentAspectRatio.value = savedAspectRatio;
				console.log('✅ 画布比例回显成功:', {
					原比例: oldRatio,
					新比例: savedAspectRatio,
					当前值: currentAspectRatio.value
				});
			} else {
				console.warn('⚠️ 无效的画布比例值:', savedAspectRatio, '使用默认值9:16');
			}
		} else {
			console.log('📐 未找到保存的画布比例信息，尝试智能推断');
			console.log('🔍 commonJson存在:', !!workData.commonJson);
			console.log('🔍 aspectRatio字段存在:', workData.commonJson?.hasOwnProperty('aspectRatio'));

			// 🧠 智能推断画布比例（针对旧作品）
			const inferredResult = inferAspectRatioFromWorkData(workData);
			if (inferredResult.confidence > 0.5) {
				const oldRatio = currentAspectRatio.value;
				currentAspectRatio.value = inferredResult.ratio;
				console.log('🧠 智能推断画布比例成功:', {
					原比例: oldRatio,
					推断比例: inferredResult.ratio,
					置信度: inferredResult.confidence,
					推断依据: inferredResult.reasons,
					当前值: currentAspectRatio.value
				});
			} else {
				console.log('🧠 智能推断置信度不足，使用默认值9:16:', {
					推断比例: inferredResult.ratio,
					置信度: inferredResult.confidence,
					推断依据: inferredResult.reasons
				});
			}
		}

		// 🖼️ 1. 背景处理（优先背景图片，其次背景颜色）
		
		// 首先检查背景图片
		if (workData.bgJson && workData.bgJson.src_url && workData.bgJson.src_url.trim() !== '') {
			const backgroundConfig = {
				type: 'image',
				value: workData.bgJson.src_url.trim()
			};

			currentBackgroundConfig.value = backgroundConfig;

			// 强制触发响应式更新
			await nextTick();
		}
		// 🎨 如果没有背景图片，检查背景颜色
		else if (workData.bgColor && typeof workData.bgColor === 'string' && workData.bgColor.trim() !== '') {
			const backgroundConfig = {
				type: 'color',
				value: workData.bgColor.trim()
			};

			currentBackgroundConfig.value = backgroundConfig;

			// 强制触发响应式更新
			await nextTick();
		} else {
			// 如果既没有背景图片也没有背景颜色，设置为默认白色
			currentBackgroundConfig.value = {
				type: 'color',
				value: '#FFFFFF'
			};
		}

		// 🎵 2. 音频轨道URL处理 
		// 修复：优先使用audioJson.wav_url，确保与音频数据的一致性
		let audioTrackUrl = '';
		
		// 优先级1：使用audioJson.wav_url（主要音频源）
		if (workData.audioJson && workData.audioJson.wav_url && workData.audioJson.wav_url.trim() !== '') {
			audioTrackUrl = workData.audioJson.wav_url.trim();
		}
		// 优先级2：降级使用videoUrl（向后兼容）
		else if (workData.videoUrl && workData.videoUrl.trim() !== '') {
			audioTrackUrl = workData.videoUrl.trim();
		}
		
		// 设置音频轨道URL到store
		if (audioTrackUrl) {
			digitalHumanStore.setTtsAudioUrl(audioTrackUrl);
		}

		// 🏷️ 3. 作品标题处理
		if (workData.title && workData.title.trim() !== '') {
			// 通过nextTick确保组件已完全挂载后再设置标题
			await nextTick();
			try {
				// 使用headbar组件暴露的setProjectTitle方法设置标题
				if (headbarRef.value && headbarRef.value.setProjectTitle) {
					headbarRef.value.setProjectTitle(workData.title);
				}
			} catch (error) {
				// 设置作品标题失败，继续执行
			}
		}

		// 🎬 4. 字幕数据处理（修复：完全以subtitle_json为准，不从其他源获取字幕）
		let subtitleDataProcessed = false; // 标记字幕数据是否已处理

		// 检查commonJson中是否存在subtitle_json字段（一切以此为准）
		if (workData.commonJson && workData.commonJson.hasOwnProperty('subtitle_json') && Array.isArray(workData.commonJson.subtitle_json)) {
			try {
				// 🔧 关键修复：即使subtitle_json是空数组，也标记为已处理，不再从其他源获取字幕
				subtitleDataProcessed = true;

				// 只有当subtitle_json不为空时，才处理字幕数据
				if (workData.commonJson.subtitle_json.length > 0) {
					// 🔄 转换字幕数据格式，确保与字幕组件兼容
					const formattedSubtitleData = workData.commonJson.subtitle_json
						.filter(item => item && item.text && item.text.trim() !== '') // 过滤空数据
						.map((item, index) => {
							const text = (item.text || '').trim();
							
							// 🕒 智能时间字段提取和单位转换
							let startTime = 0;
							let endTime = 0;
							
							// 优先级1：使用秒为单位的字段（start, end）
							if (item.start !== undefined && item.end !== undefined) {
								startTime = parseFloat(item.start) || 0;
								endTime = parseFloat(item.end) || (startTime + 1);
							}
							// 优先级2：使用兼容字段（startTime, endTime）
							else if (item.startTime !== undefined && item.endTime !== undefined) {
								startTime = parseFloat(item.startTime) || 0;
								endTime = parseFloat(item.endTime) || (startTime + 1);
							}
							// 优先级3：使用毫秒为单位的字段（time_begin, time_end），需要转换为秒
							else if (item.time_begin !== undefined && item.time_end !== undefined) {
								startTime = parseFloat(item.time_begin) / 1000 || 0; // 毫秒转秒
								endTime = parseFloat(item.time_end) / 1000 || (startTime + 1); // 毫秒转秒
							}
							// 默认处理：如果都没有，使用索引生成默认时间
							else {
								startTime = index * 2; // 默认每条字幕2秒
								endTime = startTime + 2;
							}
							
							return { 
								text, 
								startTime: startTime, 
								endTime: endTime 
							};
						});

					if (formattedSubtitleData.length > 0) {
						digitalHumanStore.setSubtitleData(formattedSubtitleData);

						// 🔧 关键修复：设置字幕显示开关为true，确保字幕能够显示
						isSubtitleVisible.value = true;

						// 🔧 增强修复：使用新的延迟重试机制同步字幕开关状态
						await nextTick();
						
						// 立即尝试同步（保留原有逻辑作为快速路径）
						try {
							if (rightOperateRef.value && rightOperateRef.value.syncAudioCaptionsState) {
								rightOperateRef.value.syncAudioCaptionsState(true);
							} else {
								throw new Error('快速路径同步失败，将使用延迟重试机制');
							}
						} catch (error) {
							// 立即同步失败，启动延迟重试机制
						}
						
						// 🔄 启动增强的延迟重试同步机制（无论立即同步是否成功都执行，确保万无一失）
						ensureSubtitleSyncWithRetry(true).then(success => {
							// 延迟重试同步机制处理完成
						}).catch(error => {
							// 延迟重试同步机制异常，但不影响主流程
						});
					} else {
						// subtitle_json不为空但过滤后为空，清空字幕数据
						digitalHumanStore.setSubtitleData([]);
					}
				} else {
					// subtitle_json为空数组，清空字幕数据
					digitalHumanStore.setSubtitleData([]);
				}
			} catch (error) {
				// 即使处理失败，也不再从其他源获取字幕
				digitalHumanStore.setSubtitleData([]);
			}
		}
		// 🎵 备选方案：只有当commonJson中完全没有subtitle_json字段时，才从audioJson获取字幕
		else if (!subtitleDataProcessed && workData.audioJson && workData.audioJson !== null) {
			// 检查是否有字幕文本数据
			if (workData.audioJson.wav_text && workData.audioJson.wav_text.trim() !== '') {
				// 创建简单的字幕数据结构
				const subtitleData = [{
					text: workData.audioJson.wav_text,
					startTime: 0,
					endTime: workData.audioJson.duration || 10 // 使用音频时长或默认10秒
				}];

				digitalHumanStore.setSubtitleData(subtitleData);
				subtitleDataProcessed = true;
			}
		}

		// 设置音频时长（无论字幕处理结果如何）
		if (workData.audioJson && workData.audioJson.duration && workData.audioJson.duration > 0) {
			digitalHumanStore.totalDuration = Math.ceil(workData.audioJson.duration);
		}

		// 🎨 5. 字体样式回显处理（从commonJson.fontStyle恢复字体配置）
		if (workData.commonJson && workData.commonJson.fontStyle && typeof workData.commonJson.fontStyle === 'object') {
			try {
				const fontStyleData = workData.commonJson.fontStyle;

				// 🎯 字体大小反向缩放转换（标准坐标系 → 页面坐标系）
				let pageFontSize = fontStyleData.fontSize || currentSubtitleConfig.value.fontSize;

				// 🔧 修复：字体大小直接使用保存的原值，不进行缩放转换
				pageFontSize = fontStyleData.fontSize;

				console.log('✅ 字体大小回显处理完成:', {
					保存的字体大小: fontStyleData.fontSize,
					回显字体大小: pageFontSize
				});

				// 🎯 从commonJson.fontStyle恢复完整的字体配置
				const fontStyleConfig = {
					fontFamily: fontStyleData.fontFamily || currentSubtitleConfig.value.fontFamily,
					fontName: fontStyleData.fontName || currentSubtitleConfig.value.fontName,
					fontUrl: fontStyleData.fontUrl || currentSubtitleConfig.value.fontUrl,  // 关键：TTF链接回显
					fontSize: pageFontSize,                                                 // 使用反向缩放后的字体大小
					textColor: fontStyleData.textColor || currentSubtitleConfig.value.textColor,
					borderColor: fontStyleData.borderColor || currentSubtitleConfig.value.borderColor,
					borderWidth: fontStyleData.borderWidth || currentSubtitleConfig.value.borderWidth
				};

				console.log('🎨 字体样式配置回显完成:', fontStyleConfig);

				// 🔄 更新字幕样式配置
				currentSubtitleConfig.value = fontStyleConfig;

				// 🎯 触发字体重新加载（如果有fontUrl）
				if (fontStyleConfig.fontUrl && fontStyleConfig.fontName) {
					await nextTick();
					// 通过PreviewEditor的字体监听机制自动触发字体加载
					// currentSubtitleConfig的变化会被PreviewEditor的watch监听到
				}

			} catch (error) {
				console.error('❌ 字体样式回显处理失败:', error);
				// 不影响其他数据的加载，继续执行
			}
		}

		// 🎨 6. 字幕样式配置处理（subtitleConfigJson）
		if (workData.subtitleConfigJson && typeof workData.subtitleConfigJson === 'object') {
			try {
				// 🎯 字体大小反向缩放转换（标准坐标系 → 页面坐标系）
				let pageFontSize = workData.subtitleConfigJson.font_size || currentSubtitleConfig.value.fontSize;

				// 🔧 修复：字体大小直接使用保存的原值，不进行缩放转换
				pageFontSize = workData.subtitleConfigJson.font_size;

				console.log('✅ 字幕配置字体大小回显处理完成:', {
					保存的字体大小: workData.subtitleConfigJson.font_size,
					回显字体大小: pageFontSize
				});

				// 🎯 样式配置映射
				const subtitleStyleConfig = {
					fontFamily: currentSubtitleConfig.value.fontFamily, // 保持原有字体ID
					fontName: currentSubtitleConfig.value.fontName,     // 保持原有字体名称
					fontSize: pageFontSize,                             // 使用反向缩放后的字体大小
					textColor: workData.subtitleConfigJson.color || currentSubtitleConfig.value.textColor,
					borderColor: workData.subtitleConfigJson.stroke_color || currentSubtitleConfig.value.borderColor,
					borderWidth: workData.subtitleConfigJson.stroke_width || currentSubtitleConfig.value.borderWidth,
					fontUrl: currentSubtitleConfig.value.fontUrl // 保持原有字体URL
				};

				// 🔄 更新字幕样式配置
				currentSubtitleConfig.value = subtitleStyleConfig;

				// 🎯 位置和尺寸配置处理
				// 等待组件更新完成后，通过PreviewEditor设置位置和尺寸
				await nextTick();
				if (previewEditorRef.value && previewEditorRef.value.setSubtitleConfig) {
					try {
						previewEditorRef.value.setSubtitleConfig(workData.subtitleConfigJson);
					} catch (error) {
						// 不影响主流程，继续执行
					}
				}

			} catch (error) {
				// 不影响其他数据的加载，继续执行
			}
		}

		// 🏞️ 7. 背景层位置回显处理
		if (workData.bgJson && typeof workData.bgJson === 'object') {
			try {
				// 等待组件更新完成后，通过PreviewEditor设置背景位置
				await nextTick();
				if (previewEditorRef.value && previewEditorRef.value.setBackgroundPosition) {
					try {
						previewEditorRef.value.setBackgroundPosition(workData.bgJson);
					} catch (error) {
						// 不影响主流程，继续执行
					}
				}

			} catch (error) {
				// 不影响其他数据的加载，继续执行
			}
		}

		// 🧑‍🎨 8. 数字人层位置回显处理
		if (workData.personJson && typeof workData.personJson === 'object') {
			try {
				// 等待组件更新完成后，通过PreviewEditor设置数字人位置
				await nextTick();
				if (previewEditorRef.value && previewEditorRef.value.setCharacterPosition) {
					try {
						previewEditorRef.value.setCharacterPosition(workData.personJson);
					} catch (error) {
						// 不影响主流程，继续执行
					}
				}

			} catch (error) {
				// 不影响其他数据的加载，继续执行
			}
		}

		// 🎵 9. 背景音乐处理（TODO: 后续实现）
		if (workData.bgmiAudioUrl && workData.bgmiAudioUrl.trim() !== '') {
			// TODO: 后续实现背景音乐设置逻辑
		}

		// 🎭 10. 第二层数字人图片URL回显处理
		if (workData.commonJson && workData.commonJson.secondDigitalHumanUrl && workData.commonJson.secondDigitalHumanUrl.trim() !== '') {
			try {
				const digitalHumanUrl = workData.commonJson.secondDigitalHumanUrl.trim();
				
				// 设置数字人配置
				const digitalHumanConfig = {
					type: 'picture',
					url: digitalHumanUrl,
					index: null  // 索引可能需要根据实际情况设置
				};

				currentDigitalHumanConfig.value = digitalHumanConfig;

				// 等待组件更新完成后，通知左侧面板同步选择状态
				await nextTick();
				if (leftOperateRef.value && leftOperateRef.value.setDigitalHumanSelection) {
					try {
						leftOperateRef.value.setDigitalHumanSelection(digitalHumanConfig);
					} catch (error) {
						// 不影响主流程，继续执行
					}
				}

			} catch (error) {
				// 不影响其他数据的加载，继续执行
			}
		}

		// 数据加载成功，显示成功提示
		ElMessage.success('作品数据加载完成');

	} catch (error) {
		loadingError.value = '加载作品数据时发生错误';
		ElMessage.error('加载作品数据失败，请稍后重试');
	} finally {
		// 无论成功还是失败，都要清除加载状态
		isLoadingWorkData.value = false;
	}
};

// ========================================
// 🎧 事件监听管理
// ========================================
/**
 * 处理来自头部组件的数据请求
 * 
 * 🎯 功能：响应头部组件的数据请求，主要用于"生成视频"功能
 * 
 * 📡 触发时机：
 * - 用户点击头部的"生成视频"按钮
 * - 其他需要获取完整编辑器数据的操作
 * 
 * 🔄 数据流程：
 * 1. 接收头部组件传递的回调函数
 * 2. 调用getCurrentEditorData获取完整配置
 * 3. 通过回调函数返回数据给头部组件
 * 4. 头部组件拿到数据后进行后续处理（如调用生成视频接口）
 * 
 * @param {Function} callback - 头部组件传递的回调函数，用于接收编辑器数据
 */
const handleDataRequest = (callback) => {
	const editorData = getCurrentEditorData();
	if (callback && typeof callback === 'function') {
		callback(editorData);
	}
};

/**
 * 监听路由参数变化
 * 
 * 🎯 功能：解决Vue Router组件复用导致onMounted不触发的问题
 * 
 * 📡 监听目标：route.query.id参数的变化
 * 
 * 🔄 处理逻辑：
 * - 当作品ID发生变化时，重新加载作品数据
 * - 支持在同一页面切换不同作品的编辑
 * - 提供字幕数据转换的备选方案
 * 
 * 🛠️ 配置说明：
 * - immediate: false - 不立即执行，避免与onMounted重复
 * - 深度监听ID变化，确保数据及时更新
 */
watch(
	() => route.query.id,
	async (newId, oldId) => {
		// 如果ID参数发生变化，重新加载作品数据
		if (newId !== oldId) {
			await loadWorkData();
			
			// 🔧 数据加载完成后，确保字幕数据正确转换（备选方案）
			setTimeout(() => {
				ensureSubtitleDataTransfer();
			}, 1000);
		}
	},
	{ immediate: false } // 不立即执行，避免与onMounted重复
);

/**
 * 组件生命周期：挂载完成
 * 
 * 🎯 功能：组件初始化时的核心逻辑
 * 
 * 📡 执行内容：
 * 1. 注册全局事件监听器
 * 2. 记录来源页面信息（备选方案）
 * 3. 加载作品数据（如果是编辑模式）
 * 4. 提供字幕数据转换的备选方案
 * 
 * 🛠️ 事件监听：
 * - 'request-digital-human-data': 头部组件的数据请求事件
 * 
 * 🔧 备选方案：
 * - 延迟1秒执行字幕数据确保转换，解决组件初始化时序问题
 * - 记录来源页面到localStorage，作为返回时的备选方案
 */
onMounted(async () => {
	// 注册事件监听器
	eventBus.on('request-digital-human-data', handleDataRequest);

	// 🎯 记录来源页面信息（备选方案）
	// 如果没有query参数中的from，则记录当前页面的referrer
	if (!route.query.from) {
		const referrer = document.referrer;
		const currentPath = window.location.pathname;
		
		// 如果referrer存在且不是当前页面，记录来源页面
		if (referrer && referrer !== window.location.href) {
			try {
				const referrerUrl = new URL(referrer);
				const referrerPath = referrerUrl.pathname;
				
				// 只记录有效的来源页面路径
				if (referrerPath && referrerPath !== currentPath) {
					localStorage.setItem('digitalHumanFromPage', referrerPath);
					console.log('📝 记录来源页面到localStorage:', referrerPath);
				}
			} catch (error) {
				console.warn('记录来源页面失败:', error);
			}
		}
	}

	// 加载作品数据（编辑模式）
	await loadWorkData();
	
	// 🔧 数据加载完成后，确保字幕数据正确转换（备选方案）
	setTimeout(() => {
		ensureSubtitleDataTransfer();
	}, 1000);
});

/**
 * 组件生命周期：组件销毁
 * 
 * 🎯 功能：清理资源，防止内存泄漏
 * 
 * 📡 清理内容：
 * - 移除全局事件监听器
 * - 确保事件总线不会持有无效的组件引用
 * 
 * 🛠️ 重要性：
 * - 防止内存泄漏
 * - 避免组件销毁后仍然响应事件
 * - 维护应用的稳定性
 */
onUnmounted(() => {
	eventBus.off('request-digital-human-data', handleDataRequest);
});

// ========================================
// 🔗 组件方法暴露
// ========================================
/**
 * 暴露给父组件或外部组件使用的方法
 *
 * 🎯 功能：
 * - getCurrentEditorData: 获取当前编辑器的完整配置数据
 * - resetUserGuide: 重置用户引导状态（开发用）
 *
 * 📡 使用场景：
 * - 头部组件的"生成视频"功能调用
 * - 保存数字人作品时收集数据
 * - 导出配置文件等功能
 * - 父组件需要获取编辑器状态时
 * - 开发测试时重置引导状态
 *
 * 🛠️ 技术说明：
 * - 使用defineExpose暴露方法，符合Vue3组合式API规范
 * - 只暴露必要的方法，保持组件封装性
 */
defineExpose({
	getCurrentEditorData,
	resetUserGuide
});
</script>

<style scoped lang="scss">
/* ========================================
   🌐 全局页面基础设置
   ======================================== */

/**
 * 全局页面样式重置
 * 
 * 🎯 设计目标：
 * - 消除浏览器默认边距和内边距
 * - 禁用页面滚动确保编辑器界面稳定
 * - 为全屏编辑器布局提供基础
 * 
 * 📱 用户体验：
 * - 避免意外滚动干扰编辑操作
 * - 确保界面在不同浏览器中的一致性
 * - 提供沉浸式的编辑体验
 */
:global(html),
:global(body) {
	margin: 0;
	padding: 0;
	overflow: hidden;
	/* 禁止页面滚动 */
}

/* ========================================
   📱 应用主容器布局系统
   ======================================== */

/**
 * 应用主容器样式
 * 
 * 🎨 布局设计：
 * - 垂直弹性布局（flex-direction: column）
 * - 全屏幕覆盖（100vh × 100%）
 * - 顶部导航 + 编辑区域的两层结构
 * 
 * 🛡️ 稳定性保障：
 * - 禁用滚动防止布局异常
 * - 零边距和内边距确保精确定位
 * - 固定尺寸避免内容溢出
 * 
 * 📐 空间分配：
 * - header: 固定高度的顶部导航
 * - main: flex:1 自动填满剩余空间
 */
.app {
	display: flex;
	flex-direction: column;
	height: 100%;
	/* 全屏高度 */
	width: 100%;
	/* 全屏宽度 - 修复水平滚动条问题 */
	overflow: hidden;
	/* 禁止滚动 */
	margin: 0;
	padding: 0;
	position: relative;
	/* 为加载遮罩层提供定位基准 */
}

/* ========================================
   📊 全局加载遮罩层样式
   ======================================== */

/**
 * 全局数据加载遮罩层
 *
 * 🎯 功能定位：
 * - 在重新编辑作品时显示数据加载状态
 * - 提供用户友好的加载提示
 * - 防止用户在数据加载期间进行操作
 *
 * 📍 定位策略：
 * - position:fixed 固定定位覆盖整个视口
 * - z-index:9999 确保在所有内容之上
 * - 半透明背景提供视觉层次感
 *
 * 🎨 视觉效果：
 * - 半透明黑色背景营造专注感
 * - 居中显示加载内容
 * - 柔和的视觉过渡效果
 */
.global-loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

/**
 * 加载内容容器
 *
 * 🎯 功能：承载加载图标和文字提示
 * 🎨 设计：白色背景卡片式设计，圆角边框
 * 📐 布局：垂直居中对齐，适当内边距
 */
.loading-content {
	background: white;
	padding: 40px;
	border-radius: 12px;
	text-align: center;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
	max-width: 300px;
}

/**
 * 加载图标样式
 *
 * 🎯 功能：提供视觉化的加载指示
 * 🎨 设计：主题色图标，旋转动画
 */
.loading-icon {
	color: #409EFF;
	margin-bottom: 16px;
	animation: rotate 1s linear infinite;
}

/**
 * 主要加载文字
 *
 * 🎯 功能：显示主要的加载状态信息
 * 🎨 设计：较大字号，深色文字
 */
.loading-text {
	font-size: 16px;
	font-weight: 500;
	color: #303133;
	margin: 0 0 8px 0;
}

/**
 * 副标题文字
 *
 * 🎯 功能：提供额外的说明信息
 * 🎨 设计：较小字号，浅色文字
 */
.loading-subtitle {
	font-size: 14px;
	color: #909399;
	margin: 0;
}

/**
 * 旋转动画
 *
 * 🎯 功能：为加载图标提供旋转效果
 */
@keyframes rotate {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

/* ========================================
   🎬 编辑器页面容器系统
   ======================================== */

/**
 * 编辑器页面容器
 * 
 * 🎯 功能定位：
 * - 承载左侧操作面板、中间编辑区域、右侧操作面板
 * - 水平三栏布局，弹性分配空间
 * - 自适应剩余空间的弹性设计
 * 
 * 📍 定位策略：
 * - flex:1 填满除顶部导航外的所有空间
 * - flex-direction:row 水平三栏布局
 * - padding-top:46px 为顶部导航预留空间
 * 
 * 🎨 视觉效果：
 * - 水平布局确保左中右功能区域清晰分工
 * - 弹性布局适配不同屏幕尺寸
 * - 禁用滚动维持界面稳定性
 */
.editor-page-container {
	flex: 1;
	display: flex;
	flex-direction: row;
	/* 水平布局 */
	padding-top: 46px;
	/* 为顶部导航预留空间 */
	overflow: hidden;
	/* 禁止滚动 */
}

/* ========================================
   🎭 中间编辑区域布局系统
   ======================================== */

/**
 * 中间编辑区域容器
 * 
 * 🎯 功能定位：
 * - 承载主预览面板和时间轴组件
 * - 垂直布局：预览面板在上，时间轴在下
 * - 在三栏布局中占据中间位置
 * 
 * 📍 定位策略：
 * - flex:1 自动占据左右侧面板之间的剩余空间
 * - flex-direction:column 垂直排列子组件
 * - align-items:center 水平居中对齐内容
 * 
 * 🎨 视觉效果：
 * - 垂直布局确保预览和时间轴层次清晰
 * - 居中对齐适配不同屏幕尺寸
 * - 弹性空间分配确保布局稳定
 */
.center-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	/* 垂直布局 */
	align-items: center;
	/* 水平居中 */
	justify-content: flex-start;
	/* 顶部对齐 */
	overflow: hidden;
	/* 禁止滚动 */
}

/* ========================================
   🎭 主预览面板设计系统
   ======================================== */

/**
 * 主面板样式
 * 
 * 🎨 设计理念：
 * - 固定尺寸确保一致的编辑体验
 * - 浅灰背景提供内容对比度
 * - 相对定位为子组件提供定位基准
 * 
 * 📐 尺寸规范：
 * - 宽度：1258px（适配主流显示器）
 * - 高度：799px（黄金比例，视觉舒适）
 * - 防压缩：flex-shrink:0 确保尺寸稳定
 * 
 * 🎯 功能特色：
 * - 承载PreviewEditor核心编辑组件
 * - 包含画布比例选择器悬浮控件
 * - 提供相对定位基准支持绝对定位子元素
 * 
 * 🌈 视觉效果：
 * - #F1F2F4背景色：柔和的浅灰色，减少视觉疲劳
 * - 与时间轴组件形成统一的视觉风格
 * 
 * 📱 响应式特性：
 * - 大屏幕：保持799px高度的完整视觉效果
 * - 小屏幕：调整为580px高度，为时间轴腾出显示空间
 */
.main-panel {
	width: 1258px;
	/* 固定宽度 */
	height: 799px;
	/* 固定高度 */
	background-color: #F1F2F4;
	/* 背景色 */
	flex-shrink: 0;
	/* 防止压缩 */
	position: relative;
	/* 为子元素提供定位上下文 */
}

/* ========================================
   📱 主面板响应式高度适配
   ======================================== */

/**
 * 笔记本等小屏幕设备适配
 * 目标：解决在笔记本上时间轴不显示的问题
 * 策略：减少主面板高度，为时间轴腾出显示空间
 */
@media (max-height: 900px) {
	.main-panel {
		height: 600px; /* 统一调整为600px，与更小屏幕保持一致 */
	}
}

/**
 * 更小屏幕设备适配
 * 目标：处理极小屏幕的边界情况
 * 策略：进一步减少主面板高度，确保时间轴完全可见
 */
@media (max-height: 800px) {
	.main-panel {
		height: 600px; /* 调整为600px，提供更好的视觉效果 */
	}
}

/* ========================================
   📐 画布比例选择器交互系统
   ======================================== */

/**
 * 画布比例选择器主容器
 * 
 * 🎨 视觉设计理念：
 * - 悬浮设计：绝对定位在主面板左下角
 * - 毛玻璃效果：半透明背景+背景模糊
 * - 轻量化界面：不干扰主编辑区域
 * 
 * 📍 定位策略：
 * - 左下角定位：bottom:2px, left:2px
 * - 高层级显示：z-index:10 确保可见性
 * - 紧凑布局：适中的padding和圆角
 * 
 * 🎭 交互体验：
 * - 轻盈的阴影效果增强层次感
 * - 背景模糊提供现代化视觉效果
 * - 小字号保持界面简洁
 * 
 * 🌟 技术亮点：
 * - backdrop-filter:blur(5px) 现代CSS毛玻璃效果
 * - rgba透明度控制精确的视觉效果
 * - box-shadow多层阴影增强立体感
 */
.aspect-ratio-selector {
	position: absolute;
	bottom: 2px;
	/* 底部定位 */
	left: 2px;
	/* 左侧定位 */
	background-color: #F1F2F4;
	/* 与外层main-panel相同的背景色 */
	border-radius: 6px;
	/* 圆角 */
	padding: 8px 12px;
	/* 内边距 */
	box-shadow: none;
	/* 去掉阴影效果 */
	font-size: 12px;
	/* 字体大小 */
	color: #333;
	/* 文字颜色 */
	backdrop-filter: none;
	/* 移除背景模糊效果 */
	z-index: 10;
	/* 确保显示在最上层 */

	/**
     * 比例下拉框主体样式
     * 
     * 🎨 设计特色：
     * - 卡片式设计：白色背景+边框+圆角
     * - Flex布局：标签、比例、图标的水平排列
     * - 交互反馈：悬停状态变化+过渡动画
     * 
     * 📐 尺寸控制：
     * - min-width:120px 确保足够的点击区域
     * - padding:6px 12px 提供舒适的内边距
     * - 12px字号保持界面紧凑
     * 
     * 🖱️ 交互设计：
     * - cursor:pointer 明确可点击性
     * - 悬停状态：边框颜色+背景色变化
     * - 0.2s过渡动画提升操作流畅性
     */
	.ratio-dropdown {
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background-color: #fff;
		/* 白色背景 */
		border: none;
		/* 去掉边框 */
		border-radius: 6px;
		/* 圆角 */
		cursor: pointer;
		/* 鼠标手型 */
		font-size: 12px;
		/* 字体大小 */
		min-width: 120px;
		/* 最小宽度 */
		transition: all 0.2s ease;
		/* 过渡效果 */

		/**
         * 悬停状态增强
         * 🎭 交互反馈：稍微加深背景色
         * 📱 用户体验：即时视觉反馈确认可交互性
         */

		/**
         * 比例标签样式
         * 🏷️ 功能：静态文本标识，说明控件用途
         * 🎨 视觉：灰色低调显示，不抢夺主要信息焦点
         */
		.ratio-label {
			color: #6b7280;
			/* 灰色文字 */
			margin-right: 8px;
			/* 右间距 */
		}

		/**
         * 当前比例显示样式
         * 🎯 功能：显示当前选中的画布比例
         * 🎨 视觉：中等字重+深色突出显示当前值
         * 📍 布局：主要内容区域，占据视觉重心
         */
		.current-ratio {
			font-weight: 500;
			/* 中等字重 */
			color: #111827;
			/* 深灰色文字 */
			margin-right: 8px;
			/* 右间距 */
		}

		/**
         * 下拉图标样式和动画
         * 
         * 🎨 视觉设计：
         * - 三角形向下箭头：直观的展开提示
         * - margin-left:auto 右对齐定位
         * - 较小字号避免视觉过重
         * 
         * 🎭 交互动画：
         * - 展开时旋转180度形成向上箭头
         * - 0.2s平滑过渡动画
         * - transform属性硬件加速
         * 
         * 📱 用户体验：
         * - 明确的状态指示
         * - 流畅的动画反馈
         * - 符合用户认知习惯
         */
		.dropdown-icon {
			margin-left: auto;
			/* 靠右对齐 */
			font-size: 10px;
			/* 图标大小 */
			color: #6b7280;
			/* 图标颜色 */
			transition: transform 0.2s ease;
			/* 旋转过渡效果 */

			/**
             * 展开状态图标动画
             * 🔄 功能：180度旋转表示菜单展开状态
             * ⚡ 性能：CSS transform硬件加速
             */
			&.expanded {
				transform: rotate(180deg);
			}
		}
	}

	/**
     * 比例选项下拉菜单样式
     * 
     * 🎨 设计理念：
     * - 卡片悬浮：脱离文档流的绝对定位
     * - 阴影层次：多层阴影营造悬浮效果
     * - 统一风格：与下拉框保持一致的视觉语言
     * 
     * 📍 定位策略：
     * - top:100% 紧贴下拉框底部
     * - left:0, right:0 与下拉框宽度对齐
     * - z-index:100 确保在所有元素之上
     * 
     * 🌟 视觉效果：
     * - 白色背景提供清晰对比
     * - 边框和圆角保持设计一致性
     * - 阴影效果增强层次感和悬浮感
     * - 2px顶部间距避免视觉粘连
     */
	.ratio-options-dropdown {
		position: absolute;
		top: 0;
		/* 与下拉框顶部对齐 */
		left: 100%;
		/* 位于下拉框右侧 */
		width: 120px;
		/* 固定宽度 */
		background-color: #fff;
		/* 白色背景 */
		border: 1px solid #d1d5db;
		/* 添加边框 */
		border-radius: 6px;
		/* 圆角 */
		box-shadow: none;
		/* 去掉阴影效果 */
		backdrop-filter: none;
		/* 移除背景模糊效果 */
		z-index: 100;
		/* 确保显示在上层 */
		margin-left: 2px;
		/* 左边距 */

		/**
         * 比例选项样式和交互
         * 
         * 🎯 功能设计：
         * - 可点击的比例选项列表
         * - 当前选中项的高亮显示
         * - 悬停状态的即时反馈
         * 
         * 🎨 视觉层次：
         * - 基础状态：普通文字颜色+透明背景
         * - 悬停状态：浅灰色背景提示可交互
         * - 激活状态：蓝色主题突出当前选择
         * 
         * 📐 布局细节：
         * - 适中的内边距提供舒适的点击区域
         * - 12px字号与下拉框保持一致
         * - cursor:pointer明确可点击性
         * 
         * 🎭 交互动画：
         * - 0.2s背景色过渡动画
         * - 平滑的视觉状态切换
         */
		.ratio-option {
			padding: 8px 12px;
			/* 内边距 */
			font-size: 12px;
			/* 字体大小 */
			cursor: pointer;
			/* 鼠标手型 */
			transition: background-color 0.2s ease;
			/* 背景色过渡 */

			/**
             * 悬停状态样式
             * 🎭 交互反馈：浅灰色背景提示可交互性
             * 📱 用户体验：即时反馈增强操作确定性
             */
			&:hover {
				background-color: #fff;
				/* 白色背景 */
			}

			/**
             * 激活状态样式
             * 
             * 🎨 主题设计：
             * - 蓝色主题：浅蓝背景+深蓝文字
             * - 字重增强：font-weight:500 突出当前选择
             * - 视觉层次：明确区分选中和未选中状态
             * 
             * 📍 语义化：.active类名清晰表达当前状态
             * 🌈 色彩心理：蓝色传达稳定和专业感
             */
			&.active {
				background-color: #fff;
				/* 白色背景 */
				color: #1d4ed8;
				/* 蓝色文字 */
				font-weight: 500;
				/* 中等字重 */
			}

			/**
             * 首尾选项圆角处理和分隔线
             * 🎨 细节优化：确保下拉菜单的圆角完整性
             * 📐 视觉统一：与容器圆角保持一致
             */
			&:first-child {
				border-radius: 5px 5px 0 0;
				border-bottom: 1px solid #e5e7eb;
				/* 添加分隔线 */
			}

			&:last-child {
				border-radius: 0 0 5px 5px;
			}
		}
	}
}
</style>