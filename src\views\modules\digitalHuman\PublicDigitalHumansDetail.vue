<template>
	<div class="public-digital-humans-detail-app">
		<!-- 顶部导航条区域 -->
		<Headbar />

		<!-- 主要内容区域 -->
		<div class="main-content">
			<div class="page-header">
				<div class="header-content">
					<h1>公共数字人</h1>
					<el-button
						type="default"
						@click="goToDigitalHumanTransition"
						class="back-button"
					>
						返回
					</el-button>
				</div>
			</div>

			<div class="content-area">
				<!-- 公共数字人展示区域 -->
				<div class="humans-container">
					<div class="humans-grid">
						<div class="human-item" v-for="(item, index) in publicDigitalHumansList" :key="item.id">
							<div class="human-avatar">
								<!-- 使用从API获取的数字人图片 -->
								<template v-for="(figure, figureIndex) in item.figures" :key="figureIndex">
									<img v-if="figure.type != 'circle_view'" :src="figure.cover" alt="公共数字人头像" />
								</template>
							</div>
							<div class="human-name">{{ item.name }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const goToDigitalHumanTransition = () => {
  router.push('/digital-human-transition')
}
// 导入统一的Headbar组件
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
// 导入数字人相关API
import { getDigitalHumanList } from '@/api/digitalHumanLeftOperate.js'
import { useloginStore } from '@/stores/login'

// 获取用户ID
const loginStore = useloginStore()
const getUserId = () => {
    return loginStore.userId || ''
}

// 公共数字人列表数据
const publicDigitalHumansList = ref([])

// 获取公共数字人列表数据
const getPublicDigitalHumansList = async () => {
    try {
        // 获取所有页面的数据，不进行分页
        let allList = []
        let currentPage = 1
        let totalPage = 1
        
        do {
            const { data: { list, page_info: { total_page } } } = await getDigitalHumanList({ page: currentPage, size: 30, userId: getUserId() })
            totalPage = total_page
            allList = allList.concat(list)
            currentPage++
        } while (currentPage <= totalPage)
        
        // 过滤掉特定ID的数字人，与原页面保持一致
        publicDigitalHumansList.value = allList.filter(item => item.id != '5474a829b22947c69a5d0e47d3b5bee7')
        console.log('获取公共数字人列表成功:', publicDigitalHumansList.value)
    } catch (error) {
        console.error('获取公共数字人列表失败:', error)
    }
}

// 页面挂载时的初始化逻辑
onMounted(() => {
    console.log('公共数字人详情页面已加载')
    // 获取公共数字人列表
    getPublicDigitalHumansList()
})
</script>

<style scoped lang="scss">
// 全屏应用容器
.public-digital-humans-detail-app {
	width: 100vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	background-color: #FFFFFF;
}

// 主要内容区域
.main-content {
	flex: 1;
	padding: 20px 20px 20px 78px;
	margin-top: 64px; // 为Headbar组件预留空间
	overflow-y: auto;
	background-color: #FFFFFF;
}

// 页面头部
.page-header {
	margin-bottom: 30px;

	.header-content {
		display: flex;
		align-items: center;
		gap: 40px; // 按钮与标题间距40px
	}

	h1 {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 32px;
		font-weight: 500;
		color: #333;
		margin: 0;
	}
	
	.back-button {
		// 设置按钮样式：白色背景，绿色边框和文字
		&.el-button--default {
			background-color: #FFFFFF;
			border-color: #0AAF60;
			color: #0AAF60;
			
			&:hover, &:focus {
				background-color: #f0f9f0; // 浅绿色背景
				border-color: #0AAF60;
				color: #0AAF60;
			}
		}
	}
}

// 内容区域
.content-area {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0; // 确保flex子项能够正确收缩
}

// 公共数字人容器
.humans-container {
	flex: 1;
	width: 1767px !important; // 保持固定宽度
	height: 100%; // 明确设置高度
	overflow: visible; // 显示所有内容，不隐藏
	display: flex;
	flex-direction: column;
}

// 数字人网格样式
.humans-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	gap: 20px;
	width: 1767px;
	padding: 0;
}

.human-item {
	text-align: center;
	cursor: pointer;
	width: 160px; // 固定宽度，与图片宽度一致
	flex-shrink: 0; // 防止收缩
	margin-bottom: 20px;
}

.human-avatar {
	position: relative;
	width: 160px;
	height: auto;
	aspect-ratio: 1/1.3;
	border-radius: 8px;
	overflow: hidden;
	background: #f0f0f0;

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.human-name {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 8px;
}

// 响应式设计
@media (max-width: 768px) {
	.main-content {
		padding: 15px;
		margin-top: 56px;
	}

	.page-header h1 {
		font-size: 24px;
	}
	
	.humans-container {
		width: 100% !important;
	}
	
	.humans-grid {
		width: 100% !important;
		justify-content: center;
	}
	
	.human-item {
		width: 140px;
	}
	
	.human-avatar {
		width: 140px;
	}
}
</style> 