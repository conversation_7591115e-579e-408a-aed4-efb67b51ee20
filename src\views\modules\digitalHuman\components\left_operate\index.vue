<template>
    <div class="leftContent">
        <div class="tabContent">
            <div v-for="(item, index) in tabList" :key="index" :class="activeTab == index ? 'active' : ''"
                @click="activeTab = index">
                <img :src="activeTab == index ? item.img_a : item.imgSrc" alt="">
                <p>{{ item.text }}</p>
            </div>
        </div>
        <div class="mainContent">
            <!-- 数字人 -->
            <div class="digitalContent" v-show="activeTab == 0" style="height: 100%;">
                <div class="list" v-infinite-scroll="loadList1" style="overflow: auto">
                    <div v-for="(item, index) in activeList1" :class="{ active: activeIndex1 == index }"
                        @click="chooseList1(index, item)">
                        <template v-for="(t, i) in item.figures" :key="i">
                            <img v-if="t.type != 'circle_view'" :src="t.cover" alt="" style="width: 95px;">
                        </template>
                        <div>{{ item.name }}</div>
                    </div>
                    <p v-if="noMoreData" style="width:100%; margin-top: 20px;text-align: center;">暂时没有数据了哦~</p>
                </div>
            </div>
            <!-- 背景 -->
            <div class="bgContent" v-show="activeTab == 1">
                <el-upload class="avatar-uploader" multiple :on-change="handleFileRequest" accept=".jpg,.png"
                    :show-file-list="false" :before-upload="beforeUpload">
                    <div class="uploadBtn">自定义上传</div>
                </el-upload>
                <div class="tabBox bgTab">
                    <div :class="{ active: activeBg == 'pattern' }" @click="changeBg('pattern')">
                        <span>图案</span>
                    </div>
                    <div :class="{ active: activeBg == 'color' }" @click="changeBg('color')">
                        <span>纯色</span>
                    </div>
                    <div :class="{ active: activeBg == 'mine' }" @click="changeBg('mine')">
                        <span>我的</span>
                    </div>
                </div>
                <!-- 图案 -->
                <div v-show="activeBg == 'pattern'" style="height: 100%;">
                    <div class="ratio">
                        <div :class="{ active: activeRatio == '9' }" @click="changeRatio('9')">9:16</div>
                        <div :class="{ active: activeRatio == '16' }" @click="changeRatio('16')">16:9</div>
                    </div>
                    <div v-show="activeRatio == '9'" class="list setHeight">
                        <div v-for="(item, index) in activeList3" :class="{ active: activeIndex3 == index }"
                            @click="chooseList3(index, item)">
                            <img :src="item.ossPath" alt="" style="width: 95px;height: 95px;">
                        </div>
                    </div>
                    <div v-show="activeRatio == '16'" class="list setHeight">
                        <div v-for="(item, index) in activeList4" :class="{ active: activeIndex4 == index }"
                            @click="chooseList4(index, item)">
                            <img :src="item.ossPath" alt="" style="width: 95px;height: 95px;">
                        </div>
                    </div>
                </div>
                <!-- 纯色 -->
                <div class="colorBox" v-show="activeBg == 'color'">
                    <div v-for="(item, index) in bgColor" :key="index" :style="{ background: item }" class="colorPicker"
                        :class="{ active: index == activeColor }" @click="changeActiveColor(index, item)"></div>
                </div>
                <!-- 我的 -->
                <div class="mineBox" v-show="activeBg == 'mine'">
                    <div v-for="(item, index) in activeList5" :class="{ active: activeIndex5 == index }"
                        @click="chooseList5(index, item)">
                        <img :src="item.storagePath" alt="" style="width: 95px;">
                    </div>
                </div>
            </div>
            <!-- 字幕 -->
            <div class="subtitle" v-show="activeTab == 2">
                <p>自定义调整</p>
                <div class="inputBox">
                    <div style="position: relative;">
                        <el-select v-model="setFontStyle" placeholder="请选择字体" style="width: 104px;height: 32px;"
                            class="custom-select" @change="handleFontStyleChange"
                            :disabled="Object.values(fontLoadingStatus).some(status => status === 'loading')">
                            <el-option v-for="(item, index) in fontListData" :label="item.name" :value="item.id" />
                        </el-select>
                        <!-- 🎨 字体加载状态指示器 -->
                        <div v-if="Object.values(fontLoadingStatus).some(status => status === 'loading')"
                            style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); font-size: 12px; color: #0AAF60;">
                            加载中...
                        </div>
                    </div>
                    <el-input-number v-model="setFontSize" :min="12" :max="100" controls-position="right"
                        @change="handleFontSizeChange" style="width: 104px;height: 32px;margin-left: 10px;" />
                </div>
                <div class="colorFrame"><span @click="showColorPicker('text')">文字色</span><el-color-picker
                        ref="textColorPicker" v-model="textColor" v-model:visible="textPicker"
                        @change="handleTextColorChange" />
                </div>
                <div class="inputBox">
                    <div class="colorFrame"><span @click="showColorPicker('border')">描边色</span><el-color-picker
                            ref="borderColorPicker" v-model="borderColor" v-model:visible="borderPicker"
                            @change="changeBorderColor" />
                    </div>
                    <span style="margin-left: 10px;">粗细</span>
                    <el-input-number v-model="setThickness" :min="0" :max="100" controls-position="right"
                        @change="handleThicknessChange" style="width: 72px;height: 32px;margin-left: 5px;" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive, ref, computed, onMounted, watch } from 'vue'
import { useloginStore } from '@/stores/login'
import { getDigitalHumanList, getBackgroundList, getFontList } from '@/api/digitalHumanLeftOperate.js'
import { ElMessage } from 'element-plus'
import { useFileUpload } from '../../utils/uploadImg.js'
import { useDigitalHumanStore } from '../../store/digitalHumanStore.js'
const { fileChange } = useFileUpload()

// ========================================
// 📡 组件通信接口定义
// ========================================
/**
 * 组件Props定义
 * 
 * 🎯 功能：接收父组件传递的画布比例状态
 * 📊 数据类型：String，格式为 '9:16' 或 '16:9'
 * 🔄 响应式：当父组件比例变化时，子组件自动同步
 */
const props = defineProps({
    currentAspectRatio: {
        type: String,
        default: '9:16',
        validator: (value) => ['9:16', '16:9'].includes(value)
    }
})
const digitalHumanStore = useDigitalHumanStore()
/**
 * 组件事件定义
 *
 * 🎯 功能：向父组件发送比例变更事件、背景变更事件、数字人选择事件和字幕样式变更事件
 * 📡 事件名：
 * - ratio-change: 比例变更事件
 * - background-change: 背景变更事件
 * - digital-human-change: 数字人选择事件
 * - subtitle-style-change: 字幕样式变更事件
 * 📊 数据格式：
 * - ratio-change: 短格式比例值（'9' 或 '16'）
 * - background-change: { type: 'color', value: '#FF0000' }
 * - digital-human-change: { type: 'picture'|'transparent', url: 'image_url', index: 0 }
 * - subtitle-style-change: { fontFamily: '1', fontSize: 18, textColor: '#ffffff', borderColor: '', borderWidth: 0 }
 */
const emit = defineEmits(['ratio-change', 'background-change', 'digital-human-change', 'subtitle-style-change']);

// ========================================
// 🔄 比例状态计算属性
// ========================================
/**
 * 当前比例的计算属性
 * 
 * 🎯 功能：将父组件的完整格式比例转换为子组件需要的短格式
 * 📊 转换规则：
 * - '9:16' → '9' （竖屏）
 * - '16:9' → '16' （横屏）
 * 
 * 🔄 响应式：当父组件比例变化时自动重新计算
 */
const activeRatio = computed(() => {
    return props.currentAspectRatio === '9:16' ? '9' : '16';
});

// 获取用户id
const loginStore = useloginStore()
const getUserId = () => {
    return loginStore.userId || ''
}

// 自定义保存的数据(回显的时候用)
const leftOperateConfigData = reactive({
    digitalHumanActiveIndex: '',//数字人选中下标
    bgActiveTab: '',
    bgActiveRatio: '',
    bgActiveIndex: '',
    digitalHumanActiveItem: null,
    bgActiveItem: null
})

// 左侧tab
const activeTab = ref(0) //默认选中tab
const tabList = reactive([
    { text: '数字人', imgSrc: new URL('@/assets/images/digitalHuman/left/digital.png', import.meta.url).href, img_a: new URL('@/assets/images/digitalHuman/left/digital_a.png', import.meta.url).href },
    { text: '背景', imgSrc: new URL('@/assets/images/digitalHuman/left/texture.png', import.meta.url).href, img_a: new URL('@/assets/images/digitalHuman/left/texture_a.png', import.meta.url).href },
    { text: '字幕', imgSrc: new URL('@/assets/images/digitalHuman/left/text.png', import.meta.url).href, img_a: new URL('@/assets/images/digitalHuman/left/text_a.png', import.meta.url).href }
])

// 右侧
// 数字人
const activeIndex1 = ref(null) //数字人列表选中
const activeList1 = ref([])
const choosedDigitalItem = ref({}) //数字人当前选中的列表项
const chooseList1 = (index, item) => {
    activeIndex1.value = index
    choosedDigitalItem.value = item
    leftOperateConfigData.digitalHumanActiveIndex = index
    leftOperateConfigData.digitalHumanActiveItem = item
    // 🎭 通知父组件数字人选择变更
    // 从数字人项目中获取图片URL
    let digitalHumanUrl = '',
        figures_type = '';
    if (item.figures && item.figures.length > 0) {
        // 查找非circle_view类型的图片
        const imageItem = item.figures.find(figure => figure.type !== 'circle_view')
        if (imageItem && imageItem.cover) {
            digitalHumanUrl = imageItem.cover
            figures_type = imageItem.type
        }
    }
    // 发送数字人变更事件
    emit('digital-human-change', {
        type: 'picture',
        url: digitalHumanUrl,
        index: index,
        name: item.name,
        figures_type: figures_type
    })
}
const digitalCurrentPage = ref(1) //数字人分页
const digitalTotalPage = ref('')
const noMoreData = ref(false) //数据是否加载完毕
// 获取数字人数据
const getDigitalList = async (page, size) => {
    const { data: { list, page_info: { total_page } } } = await getDigitalHumanList({ page: page, size: size, userId: getUserId() })
    digitalTotalPage.value = total_page
    activeList1.value = activeList1.value.concat(list)
    activeList1.value = activeList1.value.filter(item => item.id != '5474a829b22947c69a5d0e47d3b5bee7')
}
const loadList1 = () => {
    // 数字人分页数据加载
    if (digitalCurrentPage.value >= digitalTotalPage.value) {
        noMoreData.value = true
    } else {
        noMoreData.value = false
        digitalCurrentPage.value++
        getDigitalList(digitalCurrentPage.value, 100)
    }

}

// 背景
const activeBg = ref('pattern') //背景tab
const changeBg = (val) => {
    activeBg.value = val
}

// ========================================
// 🎯 比例切换联动处理函数
// ========================================
/**
 * 画布比例切换处理函数
 * 
 * 🎯 核心功能：
 * 1. 接收用户在左侧面板的比例选择
 * 2. 通过emit事件通知父组件更新画布比例
 * 3. 实现双向联动：左侧选择→画布比例→右下角选择器
 * 
 * 📡 通信流程：
 * - 用户点击9:16或16:9按钮
 * - 触发changeRatio函数
 * - emit('ratio-change', val)发送事件给父组件
 * - 父组件更新currentAspectRatio状态
 * - 通过props反向同步到子组件
 * 
 * 🔄 状态同步：
 * - 不再使用本地activeRatio.value直接赋值
 * - 依赖父组件的单一数据源保证状态一致性
 * - 避免状态冲突和不同步问题
 * - 添加值比较，防止重复发射相同事件
 * 
 * @param {string} val - 新选择的比例值（'9' 或 '16'）
 */
const changeRatio = (val) => {
    // 通过emit事件通知父组件比例变更
    emit('ratio-change', val);
}

const choosedBgItem = ref({}) //背景当前选中的列表项(图案，纯色，我的其中一个)
const activeIndex3 = ref(null) //图案-9:16列表选中
const activeList3 = ref([])
const chooseList3 = (index, item) => {
    activeIndex3.value = index
    choosedBgItem.value = item
    leftOperateConfigData.bgActiveTab = 'pattern'
    leftOperateConfigData.bgActiveRatio = '9'
    leftOperateConfigData.bgActiveIndex = index
    leftOperateConfigData.bgActiveItem = item
    activeIndex4.value = null
    activeColor.value = null
    activeIndex5.value = null
    // 🖼️ 通知父组件背景图片变更（9:16图案）
    // 发送背景变更事件
    emit('background-change', {
        type: 'image',
        value: item.ossPath
    })
}
const activeIndex4 = ref(null) //图案-16:9列表选中
const activeList4 = ref([])
const chooseList4 = (index, item) => {
    activeIndex4.value = index
    choosedBgItem.value = item
    leftOperateConfigData.bgActiveTab = 'pattern'
    leftOperateConfigData.bgActiveRatio = '16'
    leftOperateConfigData.bgActiveIndex = index
    leftOperateConfigData.bgActiveItem = item
    activeIndex3.value = null
    activeColor.value = null
    activeIndex5.value = null
    // 🖼️ 通知父组件背景图片变更（16:9图案）
    // 发送背景变更事件
    emit('background-change', {
        type: 'image',
        value: item.ossPath
    })
}
// 纯色
// 纯色颜色列表
const bgColor = ref(['#FF7043', '#FFCA28', '#D4E157', '#66BB6A', '#26C6DA', '#42A5F5', '#7E57C2', '#EC407A', '#EF5350', '#BF360C', '#FF6F00', '#827717', '#1B5E20', '#006064', '#0D47A1', '#311B92', '#880E4F', '#B71C1C', '#FF6E40', '#FFD740', '#EEFF41', '#69F0AE', '#18FFFF', '#448AFF', '#7C4DFF', '#FF4081', '#FF5252', '#DD2C00', '#FFAB00', '#AEEA00', '#00C853', '#00B8D4', '#2962FF', '#6200EA', '#C51162', '#D50000', '#555555', '#607D8B', '#9E9E9E', '#795548', '#263238', '#212121', '#3E2723', '#111111', '#DDDDDD'])
const activeColor = ref(null) //纯色列表选中
const changeActiveColor = (index, item) => {
    activeColor.value = index
    choosedBgItem.value = item
    leftOperateConfigData.bgActiveTab = 'color'
    leftOperateConfigData.bgActiveRatio = ''
    leftOperateConfigData.bgActiveIndex = index
    leftOperateConfigData.bgActiveItem = item
    activeIndex3.value = null
    activeIndex4.value = null
    activeIndex5.value = null
    // 🎨 通知父组件背景颜色变更
    // 发送背景变更事件
    emit('background-change', {
        type: 'color',
        value: item
    })
}
const activeIndex5 = ref(null) //我的列表选中
const activeList5 = ref([])
const chooseList5 = (index, item) => {
    activeIndex5.value = index
    choosedBgItem.value = item
    leftOperateConfigData.bgActiveTab = 'mine'
    leftOperateConfigData.bgActiveRatio = ''
    leftOperateConfigData.bgActiveIndex = index
    leftOperateConfigData.bgActiveItem = item
    activeIndex3.value = null
    activeIndex4.value = null
    activeColor.value = null
    // 🖼️ 通知父组件背景图片变更（我的背景）
    // 发送背景变更事件
    emit('background-change', {
        type: 'image',
        value: item.storagePath
    })
}
// 获取背景图列表
const getBgList = async () => {
    const { backgrounds, userMaterials } = await getBackgroundList({ userId: getUserId() })
    activeList3.value = backgrounds.filter(item => item.ossPath.includes('Landscape'))
    activeList4.value = backgrounds.filter(item => item.ossPath.includes('vertical'))
    activeList5.value = userMaterials
}
// 上传图片
const beforeUpload = async (file) => {
    const isJPG = file.type === 'image/jpeg'
    const isPNG = file.type === 'image/png'
    if (!isJPG && !isPNG) {
        ElMessage.error('上传文件只能是 JPG/PNG 格式!')
        return false
    }
    return true
}
const loading = ref(false)
const handleFileRequest = async (file, fileList) => {
    loading.value = true
    const uploadRequest = await fileChange(file, fileList);
    if (uploadRequest.url) {
        activeBg.value = 'mine'
        getBgList()
        activeIndex5.value = null
        // 🖼️ 通知父组件背景图片变更（上传的图片）
        // 发送背景变更事件
        emit('background-change', {
            type: 'image',
            value: uploadRequest.url
        })
    }
    loading.value = false
}

// 字幕
const setFontStyle = ref() //字体，默认为微软雅黑
const fontListData = ref([]) //后端请求的字体列表
const fontLoadingStatus = ref({}) //字体加载状态管理
const textColorPicker = ref(null) //文字色选择器
const borderColorPicker = ref(null) //描边色选择器
const textPicker = ref(false) //文字色选择器显示状态
const borderPicker = ref(false) //描边色选择器显示状态

const showColorPicker = (val) => {
    if (val == 'text') {
        if (textColorPicker.value && textPicker.value) {
            textColorPicker.value.show();
        } else {
            textColorPicker.value.hide();
        }
        textPicker.value = !textPicker.value;
    } else if (val == 'border') {
        if (borderColorPicker.value && borderPicker.value) {
            borderColorPicker.value.show();
        } else {
            borderColorPicker.value.hide();
        }
        borderPicker.value = !borderPicker.value;
    }
}
const handleFontStyleChange = () => {
    // 🎨 获取选中字体的详细信息
    const selectedFont = fontListData.value.find(font => font.id === setFontStyle.value);
    if (selectedFont) {
        // 🔄 设置字体加载状态
        fontLoadingStatus.value[selectedFont.name] = 'loading';
        // ✅ 直接使用API返回的ttf_path，无需额外API调用
        if (selectedFont.ttf_path) {
            selectedFont.dynamicFontUrl = selectedFont.ttf_path;
        }
        // 设置字体为就绪状态
        fontLoadingStatus.value[selectedFont.name] = 'ready';
    }
    // 🎨 通知父组件字幕样式变更
    emitSubtitleStyleChange()
}
const setFontSize = ref(18) //字号，与PreviewEditor默认值保持一致
const handleFontSizeChange = () => {
    // 🎨 通知父组件字幕样式变更
    emitSubtitleStyleChange()
}
const textColor = ref('#ffffff') //文字色
const handleTextColorChange = () => {
    textPicker.value = false
    // 🎨 通知父组件字幕样式变更
    emitSubtitleStyleChange()
}
const borderColor = ref('') //描边色，默认为空（无描边）
const setThickness = ref(0) //粗细，默认为0（无描边）
const handleThicknessChange = () => {
    // 🎨 通知父组件字幕样式变更
    emitSubtitleStyleChange()
}
// 改变描边色
const changeBorderColor = (value) => {
    borderPicker.value = false
    borderColor.value = value
    // 🎨 通知父组件字幕样式变更
    emitSubtitleStyleChange()
}

watch(() => digitalHumanStore['originalWorkData'], (newVal) => {
    if (newVal) {
        let data = newVal.commonJson.leftOperateConfigData,
            fontData = newVal.subtitleConfigJson;
        Object.assign(leftOperateConfigData, {
            digitalHumanActiveIndex: data.digitalHumanActiveIndex,
            bgActiveTab: data.bgActiveTab,
            bgActiveRatio: data.bgActiveRatio,
            bgActiveIndex: data.bgActiveIndex,
            digitalHumanActiveItem: data.digitalHumanActiveItem,
            bgActiveItem: data.bgActiveItem
        })
        // 数字人
        let requirePages = Math.ceil(parseFloat(parseFloat(leftOperateConfigData.digitalHumanActiveIndex) + 1) / 100) //需要回显的总页数
        if (requirePages == 1) {
            activeIndex1.value = data.digitalHumanActiveIndex
            choosedDigitalItem.value = data.digitalHumanActiveItem
        } else {
            digitalCurrentPage.value++
            setTimeout(() => {
                getDigitalList(digitalCurrentPage.value, 100)
                activeIndex1.value = data.digitalHumanActiveIndex
                choosedDigitalItem.value = data.digitalHumanActiveItem
            }, 100)
        }
        // 背景
        if (data.bgActiveTab && data.bgActiveTab != '') {
            activeBg.value = data.bgActiveTab
            if (data.bgActiveTab == 'pattern') {
                activeRatio.value = data.bgActiveRatio
                // 图案
                if (data.bgActiveRatio == '9') {
                    activeIndex3.value = data.bgActiveIndex
                } else {
                    activeIndex4.value = data.bgActiveIndex
                }
            } else if (data.bgActiveTab == 'color') {
                activeColor.value = data.bgActiveIndex
            } else if (data.bgActiveTab == 'mine') {
                activeIndex5.value = data.bgActiveIndex
            }
            choosedBgItem.value = data.bgActiveItem
        }
        // 字幕 
        if (fontData.font_id == '1') {
            setFontStyle.value = ''
        } else {
            setFontStyle.value = fontData.font_id
        }
        setFontSize.value = fontData.font_size
        // 🔧 修复：处理旧的默认描边值，转换为新的空值
        setThickness.value = (fontData.stroke_width === 7 || fontData.stroke_width === 12) ? 0 : (fontData.stroke_width || 0)
        textColor.value = fontData.color
        // 🔧 修复：处理旧的默认描边色，转换为新的空值
        borderColor.value = (fontData.stroke_color === '#000000') ? '' : (fontData.stroke_color || '')
    }
}, { deep: true, immediate: true })

// 发射字幕样式变更事件
const emitSubtitleStyleChange = () => {
    // 查找当前选中字体的详细信息
    const selectedFont = fontListData.value.find(font => font.id === setFontStyle.value);
    const fontName = selectedFont ? selectedFont.name : '微软雅黑';
    // 🎨 获取字体文件URL，优先使用API返回的ttf_path字段
    const fontUrl = selectedFont ? (
        selectedFont.dynamicFontUrl ||  // 🎯 优先使用动态获取的URL
        selectedFont.ttf_path ||        // 🎯 API返回的TTF文件路径
        selectedFont.fontUrl ||
        selectedFont.url ||
        selectedFont.ttfUrl ||
        selectedFont.fileUrl ||
        selectedFont.downloadUrl
    ) : null;
    // 构建事件数据
    const eventData = {
        fontFamily: setFontStyle.value,
        fontName: fontName,
        fontUrl: fontUrl,
        fontSize: setFontSize.value,
        textColor: textColor.value,
        borderColor: borderColor.value,
        borderWidth: Number(setThickness.value) || 0
    };
    emit('subtitle-style-change', eventData);
}
// 获取字幕列表数据
const getFontListData = async () => {
    try {
        const { data: { font_list } } = await getFontList({})
        // 🎨 处理字体列表数据，确保包含必要的字段
        fontListData.value = font_list.map(font => ({
            ...font
        }))
    } catch (error) {
        console.error('❌ 获取字体列表失败:', error)
        ElMessage.error('获取字体列表失败，请稍后重试')
    }
}

onMounted(() => {
    // 获取数字人列表
    getDigitalList(digitalCurrentPage.value, 100)
    // 获取背景图
    getBgList()
    // 获取字体列表
    getFontListData()
    // 🎨 发射初始字幕样式配置
    emitSubtitleStyleChange()
})

const clearBackgroundSelection = () => {
    try {
        // 清空所有背景选择索引
        activeIndex3.value = null;  // 9:16图案选择
        activeIndex4.value = null;  // 16:9图案选择
        activeColor.value = null;   // 纯色选择
        activeIndex5.value = null;  // 我的背景选择
        // 清空选中的背景项
        choosedBgItem.value = {};
    } catch (error) {
        console.error('❌ 清空背景选择状态失败:', error);
    }
};

/**
 * 重置字幕设置到默认值
 *
 * 🎯 功能：将所有字体相关设置重置为默认状态，用于新建模式下的数据清空
 *
 * 🧹 重置内容：
 * - 字体样式：重置为默认字体（微软雅黑）
 * - 字体大小：重置为18px
 * - 文字颜色：重置为白色
 * - 描边颜色：重置为空（无描边）
 * - 描边粗细：重置为0（无描边）
 *
 * 🔄 触发时机：
 * - 从编辑模式切换到新建模式时
 * - 父组件调用clearMiddleAreaData时
 */
const resetSubtitleSettings = () => {
    try {
        // 重置字体样式为默认值（空字符串表示微软雅黑）
        setFontStyle.value = '';

        // 重置字体大小为默认值
        setFontSize.value = 18;

        // 重置文字颜色为默认白色
        textColor.value = '#ffffff';

        // 重置描边颜色为空（无描边）
        borderColor.value = '';

        // 重置描边粗细为0（无描边）
        setThickness.value = 0;

        // 隐藏颜色选择器
        textPicker.value = false;
        borderPicker.value = false;

        // 通知父组件字幕样式已重置
        emitSubtitleStyleChange();

        console.log('✅ 左侧面板字幕设置已重置为默认值');
    } catch (error) {
        console.error('❌ 重置字幕设置失败:', error);
    }
};

defineExpose({
    // 📊 当前选中数据
    choosedDigitalItem,
    choosedBgItem,
    leftOperateConfigData,
    // 🧹 清空选择状态的方法
    clearBackgroundSelection,
    // 🔄 重置字幕设置的方法
    resetSubtitleSettings,
    // 📋 获取完整选择状态
    getSelectionData: () => ({
        digitalHuman: choosedDigitalItem.value,
        background: choosedBgItem.value,
        leftOperateConfigData,
        activeIndexes: {
            digitalHuman: activeIndex1.value,
            pattern916: activeIndex3.value,
            pattern169: activeIndex4.value,
            color: activeColor.value,
            myBackground: activeIndex5.value,
            backgroundType: activeBg.value
        }
    })
})

</script>

<style lang="scss" scoped>
* {
    box-sizing: border-box;
}

.leftContent {
    width: 336px;
    height: 100%;
    border-right: 1px solid rgba(0, 0, 0, 0.03);
    padding: 10px 0 0;
    display: flex;

    // 左侧tab
    .tabContent {
        width: 90px;
        border-right: 1px solid #E7E7E7;

        div {
            width: 62px;
            height: 62px;
            border-radius: 4px;
            margin: 40px auto 0;
            text-align: center;
            padding: 6px 10px;
            cursor: pointer;

            p {
                font-size: 14px;
                color: #000000;
                font-weight: 500;
                margin: 0;
            }
        }

        div.active {
            background: #F6F7FA;

            p {
                color: #0AAF60;
            }
        }
    }

    // 右侧内容
    .mainContent {
        width: 246px;
        height: 100%;

        // tab公共样式
        .tabBox {
            border-bottom: 1px solid #E7E7E7;
            display: flex;
            justify-content: space-between;

            div {
                text-align: center;
                cursor: pointer;

                span {
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.45);
                    padding: 10px 5px;
                    display: inline-block;
                }

                span:hover {
                    color: #0AAF60;
                }
            }

            div.active {
                span {
                    color: #000000;
                    font-weight: 500;
                    border-bottom: 2px solid #0AAF60;
                }
            }
        }

        // 数字人tab专用
        // .digitalTab {
        //     margin: 25px 0 0 0;

        //     div {
        //         width: 50%;
        //     }
        // }

        // 背景tab专用
        .bgTab {
            div {
                width: 33.3%;
            }
        }

        .list {
            display: flex;
            flex-wrap: wrap;
            height: calc(100% - 20px);
            overflow: auto;
            scrollbar-width: none; //Firefox
            -ms-overflow-style: none; //IE 和 Edge

            div {
                width: 50%;
                text-align: center;
                margin: 20px 0 10px 0;
                cursor: pointer;

                img {
                    border: 2px solid transparent;
                    border-radius: 4px
                }

                div {
                    font-size: 12px;
                    color: #000000;
                    margin: 5px auto 0;
                }
            }

            div.active {
                img {
                    border-color: #0AAF60;

                }

                div {
                    color: #0AAF60;
                }
            }

        }

        .list::-webkit-scrollbar {
            display: none;
        }

        // 背景-图案-列表的高度
        .setHeight {
            height: calc(100% - 170px);
        }

        // 背景
        .bgContent {
            height: 100%;

            .uploadBtn {
                width: 210px;
                height: 36px;
                border: 1px solid #0AAF60;
                border-radius: 2px;
                font-size: 14px;
                color: #0AAF60;
                margin: 20px 0 20px 18px;
                text-align: center;
                line-height: 36px;
            }

            // 图案9:16 16:9
            .ratio {
                display: flex;
                margin: 20px 0 0 20px;

                div {
                    width: 59px;
                    height: 32px;
                    font-size: 14px;
                    color: #606266;
                    text-align: center;
                    line-height: 32px;
                    border: 1px solid #DCDFE6;
                    cursor: pointer;
                }

                div:first-child {
                    border-top-left-radius: 4px;
                    border-bottom-left-radius: 4px;
                }

                div:last-child {
                    border-top-right-radius: 4px;
                    border-bottom-right-radius: 4px;
                    border-left: none;
                }

                div.active {
                    background: #0AAF60;
                    color: #FFFFFF;
                    border: none;
                }
            }

            // 纯色
            .colorBox,
            .mineBox {
                display: flex;
                flex-wrap: wrap;
                height: calc(100% - 130px);
                overflow-y: scroll;
                scrollbar-width: none; //Firefox
                -ms-overflow-style: none; //IE 和 Edge
                align-content: flex-start;
            }

            .colorBox {
                .colorPicker {
                    width: 95px;
                    height: 95px;
                    border-radius: 4px;
                    margin: 30px 0px 0 18px;
                    cursor: pointer;
                }

                div.active {
                    border: 2px solid #0AAF60;
                }
            }

            .colorBox::-webkit-scrollbar,
            .mineBox::-webkit-scrollbar {
                display: none;
            }

            .mineBox {
                div {
                    width: 50%;
                    text-align: center;
                    cursor: pointer;
                    margin: 20px 0 0 0;

                    img {
                        border: 2px solid transparent;
                        border-radius: 4px
                    }
                }

                div.active {
                    img {
                        border-color: #0AAF60;
                    }
                }
            }

        }

        // 字幕
        .subtitle {
            padding: 0 0 0 15px;

            p {
                font-size: 14px;
                color: #000000;
                font-weight: 500;
                margin: 30px 0 30px 0;
            }

            .inputBox {
                display: flex;
                align-items: center;
                margin: 10px 0 10px 0;

                ::v-deep(.el-select__placeholder) {
                    color: #454545;
                    font-size: 14px;
                }

                span {
                    color: #454545;
                    font-size: 14px;
                }

                .colorFrame {
                    ::v-deep(.el-color-picker__color-inner) {
                        display: none !important;
                    }

                    ::v-deep(.el-color-picker__color) {
                        border: 1px solid v-bind('borderColor');

                    }
                }

            }

            .colorFrame {
                width: 104px;
                height: 32px;
                border: 1px solid #DCDCDC;
                border-radius: 3px;
                line-height: 32px;
                padding: 0 0 0 12px;
                position: relative;
                display: flex;
                justify-content: space-around;

                ::v-deep(.el-color-picker__trigger) {
                    border: none;
                }

                ::v-deep(.el-color-picker__color) {
                    width: 12px;
                    height: 12px;
                    border-radius: 2px;
                    border: 1px solid #000000;

                }

                ::v-deep(.el-icon svg) {
                    display: none;
                }

                span {
                    cursor: pointer;
                }
            }
        }
    }
}
</style>
<style lang="scss">
.el-select__wrapper.is-focused,
.el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px #0AAF60 inset !important;
}

.el-select-dropdown__item.is-selected {
    color: #0AAF60 !important;
}

.el-input-number__decrease,
.el-input-number__increase {
    background: #fff;
}

.el-input-number__decrease:hover~.el-input:not(.is-disabled) .el-input__wrapper,
.el-input-number__increase:hover~.el-input:not(.is-disabled) .el-input__wrapper {
    box-shadow: 0 0 0 1px #0AAF60 inset !important;
}

.el-icon:hover {
    color: #0AAF60 !important;
}
</style>
